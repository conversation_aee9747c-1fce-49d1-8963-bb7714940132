//START OF FILE: main/Assignment2.java
package main;

import bus.uigen.OEFrame;
import bus.uigen.ObjectEditor;
import mp.bridge.BridgeScene;
import mp.shapes.ConsoleScene;
import mp.shapes.ConsoleSceneInterface;

public class Assignment2 {
    public static final long SLEEP_SECONDS = 50;
    public static void main(String[] args) throws InterruptedException {
    	final BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        final OEFrame frame = ObjectEditor.edit(scene);

        // Create and register console scene observer using factory method
        final ConsoleSceneInterface consoleScene = StaticFactoryClass.consoleSceneViewFactoryMethod();
        ConsoleScene.registerWithBridgeScene(scene, consoleScene);

        // Demo the scene methods - no need for manual refresh calls
        scene.approach(scene.getArthur());
        Thread.sleep(SLEEP_SECONDS);
        scene.say("What is your name?");
        Thread.sleep(SLEEP_SECONDS);
        scene.say("<PERSON>, King of the Britons");
        Thread.sleep(SLEEP_SECONDS);
        scene.say("What is your quest?");
        Thread.sleep(SLEEP_SECONDS);
        scene.say("To seek the Holy Grail");
        Thread.sleep(SLEEP_SECONDS);
        scene.passed();
        Thread.sleep(SLEEP_SECONDS);

        scene.approach(scene.getLancelot());
        Thread.sleep(SLEEP_SECONDS);
        scene.say("What is your favorite color?");
        Thread.sleep(SLEEP_SECONDS);
        scene.say("Blue");
        Thread.sleep(SLEEP_SECONDS);
        scene.failed();
    }
}

//END OF FILE
//START OF FILE: main/BridgeSceneImpl.java
package main;
import mp.bridge.ArthurHead;
import mp.bridge.LancelotHead;
import mp.bridge.RobinHead;
import mp.bridge.GalahadHead;
import mp.bridge.GuardHead;
import mp.bridge.Avatar;
import mp.bridge.AvatarImpl;
import mp.bridge.BridgeScene;
import mp.shapes.Gorge;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScaleRectangle;
import mp.shapes.AScaleRectangleInterface;
import mp.shapes.GetRectangle;
import util.annotations.Visible;
@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class BridgeSceneImpl implements BridgeScene {
    private final Avatar arthur, lancelot, robin, galahad, guard;
    public static final int SOME_AX = 10;
    public static final int SOME_AY = 50;
    public static final int LANCELOT_CONSTANT = 8;
    public static final int ROBIN_CONSTANT = 15;
    public static final int GAL_CONSTANT = 22;
    public static final int GUARD_CONSTANT = 30;
    private GetRectangle gorge;
    private Avatar cur;
    private AScaleRectangleInterface knightArea;
    private AScaleRectangleInterface guardArea;
    private boolean knightTurn = false;
    private static final int AREA_AX = 500;
    private static final int KNIGHT_AY = 600; 
    private static final int GUARD_AY = 350;
    private static final int AREA_WIDTH = 120;
    private static final int AREA_HEIGHT = 100;
    private boolean occupied = false;
    private static final int GORGE_AX = 750;
    private static final int RIGHT_SIDE_X = 900; // Right side of gorge for passed knights
    private static int number = 0;
    private static final int CONSTANT = 50;
    public BridgeSceneImpl() {
      // Create avatars
      arthur = new AvatarImpl(new ArthurHead());
      lancelot = new AvatarImpl(new LancelotHead());
      robin = new AvatarImpl(new RobinHead());
      galahad = new AvatarImpl(new GalahadHead());
      guard = new AvatarImpl(new GuardHead());

      // Place knights on left side of gorge, with no knight in standing area
      arthur.move(SOME_AX, SOME_AY);
      lancelot.move(SOME_AX*LANCELOT_CONSTANT, SOME_AY);
      robin.move(SOME_AX*ROBIN_CONSTANT, SOME_AY);
      galahad.move(SOME_AX*GAL_CONSTANT, SOME_AY);

      // Place guard in guard standing area
      guard.move(AREA_AX, GUARD_AY);

      // Create gorge and standing areas
      gorge = new Gorge(GORGE_AX);
      knightArea = new AScaleRectangle(AREA_AX, KNIGHT_AY, AREA_WIDTH, AREA_HEIGHT);
      guardArea = new AScaleRectangle(AREA_AX, GUARD_AY, AREA_WIDTH, AREA_HEIGHT);
    }
    @Override
    public void passed(){
    	if(occupied && !knightTurn) { // Only if occupied and it's guard's turn
    		cur.move(RIGHT_SIDE_X, KNIGHT_AY); // Move knight to right side of gorge
    		occupied = false;
    		cur = null;
    		knightTurn = false; // Reset for next knight
    	}
    }
    @Override
    public void failed(){
    	if(occupied) {
    		if(!knightTurn) {
    			// Guard's turn to speak, so knight fails
    			cur.getHead().setX(GORGE_AX);
    			cur.getHead().setY(number);
    			number += CONSTANT;
    			occupied = false; // Knight area becomes unoccupied
    			cur = null;
    			knightTurn = false; // Reset for next knight
    		} else {
    			// Knight's turn to speak, so guard fails
    			guard.getHead().setX(GORGE_AX);
    			guard.getHead().setY(number);
    			number += CONSTANT;
    			// Knight area remains occupied, guard just falls
    		}
    	}
    }
    @Override
    public void approach(final Avatar avatar){
    	if(!occupied) {
    		avatar.move(AREA_AX, KNIGHT_AY);
    		occupied = true;
    		cur = avatar;
    	}
    	// Do nothing if already occupied
    }
    @Override
    public void say(final String say){
    	if(!occupied) {
    		return; // Do nothing if knight area is not occupied
    	}

    	if(!knightTurn) {
    		// Guard's turn to speak
    		guard.getStringShape().setText(say);
    		knightTurn = true; // Next turn will be knight's
    	} else {
    		// Knight's turn to speak
    		cur.getStringShape().setText(say);
    		knightTurn = false; // Next turn will be guard's
    	}
    }
    @Override
    public Avatar getArthur(){ 
    	return arthur; 
    }
    @Override
    public Avatar getLancelot(){ 
    	return lancelot; 
    }
    @Override
    public Avatar getRobin(){ 
    	return robin; 
    }
    @Override
    public Avatar getGalahad(){ 
    	return galahad; 
    }
    @Override
    public Avatar getGuard(){ 
    	return guard; 
    }
    @Override
    public AScaleRectangleInterface getKnightArea() {
        return knightArea;
    }
    @Override
    public AScaleRectangleInterface getGuardArea() {
        return guardArea;
    }
    @Override
    public GetRectangle getGorge() {return gorge;}
    @Override
    public boolean getOccupied() {return occupied;}
    @Override
    public boolean getKnightTurn() {return knightTurn;}

    @Override
    @Visible(false)
    public Avatar getInteractingKnight() {
        return occupied ? cur : null;
    }

    public void scroll(int deltaX, int deltaY) {
        // Scroll all avatars by the given delta
        arthur.move(deltaX, deltaY);
        lancelot.move(deltaX, deltaY);
        robin.move(deltaX, deltaY);
        galahad.move(deltaX, deltaY);
        guard.move(deltaX, deltaY);

        // Scroll the standing areas and gorge
        knightArea.setX(knightArea.getX() + deltaX);
        knightArea.setY(knightArea.getY() + deltaY);
        guardArea.setX(guardArea.getX() + deltaX);
        guardArea.setY(guardArea.getY() + deltaY);
        gorge.getLeftLine().move(deltaX, deltaY);
        gorge.getRightLine().move(deltaX, deltaY);
        gorge.getRectangle().setX(gorge.getRectangle().getX() + deltaX);
        gorge.getRectangle().setY(gorge.getRectangle().getY() + deltaY);
    }
}
//END OF FILE
//START OF FILE: main/RunSS25A2Tests.java
package main;

import grader.basics.execution.BasicProjectExecution;
import gradingTools.comp301ss24.assignment2.SS24Assignment2Suite;
import trace.grader.basics.GraderBasicsTraceUtility;
public class RunSS25A2Tests {
	 private static final int MAX_PRINTED_TRACES = 600;
		 private static final int MAX_TRACES         = 2000;
		 private static final int PROCESS_TIME  = 5;
		public static void main(String[] args) {
			// if you set this to false, grader steps will not be traced
			GraderBasicsTraceUtility.setTracerShowInfo(true);	
			// if you set this to false, all grader steps will be traced,
			// not just the ones that failed		
			GraderBasicsTraceUtility.setBufferTracedMessages(true);
			// Change this number if a test trace gets longer than 600 and is clipped
			GraderBasicsTraceUtility.setMaxPrintedTraces(MAX_PRINTED_TRACES);
			// Change this number if all traces together are longer than 2000
			GraderBasicsTraceUtility.setMaxTraces(MAX_TRACES);
			// Change this number if your process times out prematurely
			BasicProjectExecution.setProcessTimeOut(PROCESS_TIME);
			// You need to always call such a method
		SS24Assignment2Suite.main(args);
	}
}

//END OF FILE
//START OF FILE: main/StaticFactoryClass.java
package main;
import mp.bridge.Angle;
import mp.bridge.Shape;
import mp.shapes.ConsoleScene;
import mp.shapes.ConsoleSceneInterface;
import mp.bridge.BridgeScene;
import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.FACTORY_CLASS)
public class StaticFactoryClass{
static BridgeScene scene;	
private static ConsoleSceneInterface consoleView;
@Tags(Comp301Tags.BRIDGE_SCENE)
public static BridgeScene bridgeSceneFactoryMethod(){
   if (scene == null){scene = new BridgeSceneImpl();}
   return scene;
}
@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public static ConsoleSceneInterface consoleSceneViewFactoryMethod() {
  if (consoleView == null) {
    consoleView = ConsoleScene.consoleSceneViewFactoryMethod();
  }
  return consoleView;
}
@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod(){
	   return new Shape();
}

@Tags(Comp301Tags.ANGLE)
public static Angle legsFactoryMethod(double leftRadius, double leftAngle, double rightRadius, double rightAngle){
	   return new Shape(leftRadius, leftAngle, rightRadius, rightAngle);
}
}
//END OF FILE
//START OF FILE: mp/bridge/AbstractImageShape.java
package mp.bridge;

import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Visible;
import util.models.PropertyListenerRegisterer;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.List;
import java.util.ArrayList;

@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public abstract class AbstractImageShape implements ImageShape, PropertyListenerRegisterer {
    protected String imageFileName;
    protected int x, y;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();
    
    public AbstractImageShape(String defaultImageFileName) {
        this.imageFileName = defaultImageFileName;
        this.x = 0;
        this.y = 0;
    }
    
    @Override
    public String getImageFileName() {
        return imageFileName;
    }
    
    @Override
    public void setImageFileName(String file) {
        String oldFileName = this.imageFileName;
        this.imageFileName = file;
        notify("ImageFileName", oldFileName, file);
    }
    
    @Override
    public int getX() {
        return x;
    }
    
    @Override
    public void setX(int x) {
        int oldX = this.x;
        this.x = x;
        notify("X", oldX, x);
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(int y) {
        int oldY = this.y;
        this.y = y;
        notify("Y", oldY, y);
    }



    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        propertyChangeListeners.add(listener);
    }

    @Visible(false)
    public void notify(String property, Object old, Object current) {
        PropertyChangeEvent event = new PropertyChangeEvent(this, property, old, current);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }
}

//END OF FILE
//START OF FILE: mp/bridge/AbstractStringShape.java
package mp.bridge;

import util.annotations.Visible;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import util.models.PropertyListenerRegisterer;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.List;
import java.util.ArrayList;

@StructurePattern(StructurePatternNames.STRING_PATTERN)
public abstract class AbstractStringShape implements StringShape, PropertyListenerRegisterer {
    protected String text;
    protected int x, y;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();
    
    public AbstractStringShape(String defaultText) {
        this.text = defaultText;
        this.x = 0;
        this.y = 0;
    }
    
    @Override
    public String getText() {
        return text;
    }
    
    @Override
    public void setText(String text) {
        String oldText = this.text;
        this.text = text;
        notify("Text", oldText, text);
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(int x) {
        int oldX = this.x;
        this.x = x;
        notify("X", oldX, x);
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(int y) {
        int oldY = this.y;
        this.y = y;
        notify("Y", oldY, y);
    }

    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        propertyChangeListeners.add(listener);
    }

    @Visible(false)
    public void notify(String property, Object old, Object current) {
        PropertyChangeEvent event = new PropertyChangeEvent(this, property, old, current);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }
}

//END OF FILE
//START OF FILE: mp/bridge/Angle.java
package mp.bridge;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import mp.shapes.Get;
import mp.shapes.Moveable;   
@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Angle extends Moveable, Get{
}

//END OF FILE
//START OF FILE: mp/bridge/ArthurHead.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.PropertyNames;
import util.annotations.EditablePropertyNames;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
@PropertyNames({"X", "Y", "ImageFileName"})
@EditablePropertyNames({"X", "Y", "ImageFileName"})
public class ArthurHead extends AbstractImageShape {
    public ArthurHead() {
        super("images/arthur.jpg");
    }
}

//END OF FILE
//START OF FILE: mp/bridge/Avatar.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface Avatar extends Moveable{
    ImageShape getHead();
    StringShape getStringShape();
    Angle getArms();
    Angle getLegs();
    void move(int deltaX, int deltaY);
}

//END OF FILE
//START OF FILE: mp/bridge/AvatarImpl.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import main.StaticFactoryClass;
import mp.shapes.Locatable;
import mp.shapes.Moveable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class AvatarImpl implements Avatar{
    private final ImageShape head;
    private final StringShape speech;
    private final Angle arms;
    private final Angle legs;

    public AvatarImpl(final ImageShape h) {
        this.head = h;
        this.speech = new SpeechBubble();
        this.arms = StaticFactoryClass.legsFactoryMethod();
        this.legs = StaticFactoryClass.legsFactoryMethod();
        layoutAtOrigin();
    }
    private void layoutAtOrigin(){
    }
    @Override
    public ImageShape getHead(){ 
    	return head; 
    }
    @Override
    public StringShape getStringShape() {
        return speech;
    }
    @Override
    public Angle getArms(){
    	return arms; 
    }
    @Override
    public Angle getLegs(){ 
    	return legs; 
    }
    @Override
    public void move(final int deltaX, final int deltaY) {
        head.setX(head.getX() + deltaX);
        head.setY(head.getY() + deltaY);  
        arms.move(deltaX, deltaY);
        legs.move(deltaX, deltaY);
        speech.setX(speech.getX() + deltaX);
        speech.setY(speech.getY() + deltaY);
        layoutAtOrigin();
    }
}
//END OF FILE
//START OF FILE: mp/bridge/BridgeScene.java
package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.AScaleRectangleInterface;
import mp.shapes.GetRectangle;
import tags301.Comp301Tags;

@Tags(Comp301Tags.BRIDGE_SCENE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public interface BridgeScene {
    Avatar getArthur();
    Avatar getLancelot();
    Avatar getRobin();
    Avatar getGalahad();
    Avatar getGuard();
    public void passed();
    public void failed();
    public void approach(final Avatar avatar);
    public void say(final String say);
    public AScaleRectangleInterface getKnightArea();
    public AScaleRectangleInterface getGuardArea();
    public GetRectangle getGorge();
    public boolean getOccupied();
    public boolean getKnightTurn();
    public Avatar getInteractingKnight();
}
//END OF FILE
//START OF FILE: mp/bridge/GalahadHead.java
package mp.bridge;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.PropertyNames;
import util.annotations.EditablePropertyNames;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
@PropertyNames({"X", "Y", "ImageFileName"})
@EditablePropertyNames({"X", "Y", "ImageFileName"})
public class GalahadHead extends AbstractImageShape{
    public GalahadHead() {
        super("images/galahad.jpg");
    }
}

//END OF FILE
//START OF FILE: mp/bridge/GuardHead.java
package mp.bridge;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.PropertyNames;
import util.annotations.EditablePropertyNames;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
@PropertyNames({"X", "Y", "ImageFileName"})
@EditablePropertyNames({"X", "Y", "ImageFileName"})
public class GuardHead extends AbstractImageShape {
    public GuardHead() {
        super("images/Guard.jpg");
    }
}

//END OF FILE
//START OF FILE: mp/bridge/ImageShape.java
package mp.bridge;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@Tags(Comp301Tags.BOUNDED_SHAPE)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public interface ImageShape extends Locatable{
    String getImageFileName();
    void setImageFileName(String file);
}

//END OF FILE
//START OF FILE: mp/bridge/LancelotHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import util.annotations.PropertyNames;
import util.annotations.EditablePropertyNames;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
@PropertyNames({"X", "Y", "ImageFileName"})
@EditablePropertyNames({"X", "Y", "ImageFileName"})
public class LancelotHead extends AbstractImageShape{
    public LancelotHead() {
        super("images/lancelot.jpg");
    }
}

//END OF FILE
//START OF FILE: mp/bridge/RobinHead.java
package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import util.annotations.PropertyNames;
import util.annotations.EditablePropertyNames;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
@PropertyNames({"X", "Y", "ImageFileName"})
@EditablePropertyNames({"X", "Y", "ImageFileName"})
public class RobinHead extends AbstractImageShape{
    public RobinHead() {
        super("images/robin.jpg");
    }
}

//END OF FILE
//START OF FILE: mp/bridge/Shape.java
package mp.bridge;
import mp.shapes.Get;
import mp.shapes.RotateLine;
import mp.shapes.RotatingLine;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;

@Tags(Comp301Tags.ANGLE)
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class Shape implements Angle{
    private final RotateLine left, right;
    public Shape() {
      left  = new RotatingLine();
      right = new RotatingLine();
    }

    public Shape(double leftRadius, double leftAngle, double rightRadius, double rightAngle) {
      left  = new RotatingLine();
      right = new RotatingLine();
      left.setRadius(leftRadius);
      left.setAngle(leftAngle);
      right.setRadius(rightRadius);
      right.setAngle(rightAngle);
    }
    @Override
    public RotateLine getLeftLine()  { 
    	return left; 
    }
    @Override
    public RotateLine getRightLine() { 
    	return right; 
    }
    @Override
    public void move(int deltaX, int deltaY) {
        left.move(deltaX, deltaY);
        right.move(deltaX, deltaY);
    }
}

//END OF FILE
//START OF FILE: mp/bridge/SpeechBubble.java
package mp.bridge;

public class SpeechBubble extends AbstractStringShape{
    public SpeechBubble() {
        super("Grail");
    }
}

//END OF FILE
//START OF FILE: mp/bridge/StringShape.java
package mp.bridge;

import mp.shapes.Locatable;

public interface StringShape extends Locatable{
    String getText();
    void setText(String at);
}

//END OF FILE
//START OF FILE: mp/shapes/AbstractAScaleRectangle.java
package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Visible;
import util.models.PropertyListenerRegisterer;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.List;
import java.util.ArrayList;
import tags301.Comp301Tags;

@StructurePattern(StructurePatternNames.LINE_PATTERN)
public abstract class AbstractAScaleRectangle implements AScaleRectangleInterface, PropertyListenerRegisterer {
    protected int x, y, width, height;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();
    
    public AbstractAScaleRectangle(int x, int y, int width, int height) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
    }
    
    @Override
    public int getX() {
        return x;
    }
    
    @Override
    public void setX(int x) {
        int oldX = this.x;
        this.x = x;
        notify("X", oldX, x);
    }
    
    @Override
    public int getY() {
        return y;
    }
    
    @Override
    public void setY(int y) {
        int oldY = this.y;
        this.y = y;
        notify("Y", oldY, y);
    }
    
    @Override
    public int getWidth() {
        return width;
    }
    
    @Override
    public void setWidth(int width) {
        int oldWidth = this.width;
        this.width = width;
        notify("Width", oldWidth, width);
    }
    
    @Override
    public int getHeight() {
        return height;
    }
    
    @Override
    public void setHeight(int height) {
        int oldHeight = this.height;
        this.height = height;
        notify("Height", oldHeight, height);
    }

    @Override
    public void scale(int percentage) {
        int oldWidth = this.width;
        int oldHeight = this.height;
        this.width = (this.width * percentage) / 100;
        this.height = (this.height * percentage) / 100;
        notify("Width", oldWidth, this.width);
        notify("Height", oldHeight, this.height);
    }

    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        propertyChangeListeners.add(listener);
    }

    @Visible(false)
    public void notify(String property, Object old, Object current) {
        PropertyChangeEvent event = new PropertyChangeEvent(this, property, old, current);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }
}

//END OF FILE
//START OF FILE: mp/shapes/AbstractBoundedShape.java
package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;

@StructurePattern(StructurePatternNames.LINE_PATTERN)
public abstract class AbstractBoundedShape extends AbstractLocatable implements BoundedShape {
    protected int width, height;
    
    public AbstractBoundedShape(int x, int y, int width, int height) {
        super(x, y);
        this.width = width;
        this.height = height;
    }
    
    @Override
    public int getWidth() {
        return width;
    }
    
    @Override
    public void setWidth(int width) {
        int oldWidth = this.width;
        this.width = width;
        notify("Width", oldWidth, width);
    }
    
    @Override
    public int getHeight() {
        return height;
    }
    
    @Override
    public void setHeight(int height) {
        int oldHeight = this.height;
        this.height = height;
        notify("Height", oldHeight, height);
    }
}

//END OF FILE
//START OF FILE: mp/shapes/AbstractLocatable.java
package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Visible;
import util.models.PropertyListenerRegisterer;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.List;
import java.util.ArrayList;
import tags301.Comp301Tags;

@StructurePattern(StructurePatternNames.LINE_PATTERN)
public abstract class AbstractLocatable implements Locatable, PropertyListenerRegisterer {
    protected int x, y;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();
    
    public AbstractLocatable() {
        this.x = 0;
        this.y = 0;
    }
    
    public AbstractLocatable(int x, int y) {
        this.x = x;
        this.y = y;
    }
    
    @Override
    public int getX() {
        return x;
    }
    
    @Override
    public void setX(int x) {
        int oldX = this.x;
        this.x = x;
        notify("X", oldX, x);
    }
    
    @Override
    public int getY() {
        return y;
    }
    
    @Override
    public void setY(int y) {
        int oldY = this.y;
        this.y = y;
        notify("Y", oldY, y);
    }
    
    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        propertyChangeListeners.add(listener);
    }
    
    @Visible(false)
    public void notify(String property, Object old, Object current) {
        PropertyChangeEvent event = new PropertyChangeEvent(this, property, old, current);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }
}

//END OF FILE
//START OF FILE: mp/shapes/AbstractPolarPoint.java
package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;

@StructurePattern(StructurePatternNames.LINE_PATTERN)
public abstract class AbstractPolarPoint extends AbstractLocatable implements PolarPointInterface {
    protected double radius, angle;
    
    public AbstractPolarPoint(double radius, double angle) {
        super();
        this.radius = radius;
        this.angle = angle;
        updateCartesian();
    }
    
    public AbstractPolarPoint(int x, int y) {
        super(x, y);
        updatePolar();
    }
    
    @Override
    public double getRadius() {
        return radius;
    }
    
    @Override
    public void setRadius(double radius) {
        double oldRadius = this.radius;
        this.radius = radius;
        updateCartesian();
        notify("Radius", oldRadius, radius);
    }
    
    @Override
    public double getAngle() {
        return angle;
    }
    
    @Override
    public void setAngle(double angle) {
        double oldAngle = this.angle;
        this.angle = angle;
        updateCartesian();
        notify("Angle", oldAngle, angle);
    }
    
    private void updateCartesian() {
        int oldX = this.x;
        int oldY = this.y;
        this.x = (int) (radius * Math.cos(angle));
        this.y = (int) (radius * Math.sin(angle));
        notify("X", oldX, this.x);
        notify("Y", oldY, this.y);
    }
    
    private void updatePolar() {
        double oldRadius = this.radius;
        double oldAngle = this.angle;
        this.radius = Math.sqrt(x * x + y * y);
        this.angle = Math.atan2(y, x);
        notify("Radius", oldRadius, this.radius);
        notify("Angle", oldAngle, this.angle);
    }
    
    @Override
    public void setX(int x) {
        super.setX(x);
        updatePolar();
    }
    
    @Override
    public void setY(int y) {
        super.setY(y);
        updatePolar();
    }
}

//END OF FILE
//START OF FILE: mp/shapes/AbstractRotateLine.java
package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Visible;
import util.models.PropertyListenerRegisterer;
import tags301.Comp301Tags;

@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public abstract class AbstractRotateLine implements RotateLine {
    protected PolarPointInterface point;
    protected int x, y;
    protected static final double UNIT = Math.PI / 32;

    public AbstractRotateLine() {
        this.x = 0;
        this.y = 0;
        this.point = new APolarPoint(0, 0);
    }

    @Override
    public int getX() {
        return x + point.getX();
    }

    @Override
    public void setX(int x) {
        notify("X", this.x, x);
        this.x = x;
    }

    @Override
    public int getY() {
        return y + point.getY();
    }

    @Override
    public void setY(int y) {
        notify("Y", this.y, y);
        this.y = y;
    }

    @Override
    public int getWidth() {
        return point.getX();
    }

    @Override
    public int getHeight() {
        return point.getY();
    }

    @Override
    public void setWidth(int width) {
        notify("Width", getWidth(), width);
    }

    @Override
    public void setHeight(int height) {
        notify("Height", getHeight(), height);
    }

    @Override
    public double getRadius() {
        return point.getRadius();
    }

    @Override
    public void setRadius(double radius) {
        double oldRadius = point.getRadius();
        point = new APolarPoint(radius, point.getAngle());
        notify("Radius", oldRadius, radius);
        notify("Width", getWidth(), getWidth());
        notify("Height", getHeight(), getHeight());
    }

    @Override
    public double getAngle() {
        return point.getAngle();
    }

    @Override
    public void setAngle(double angle) {
        double oldAngle = point.getAngle();
        point = new APolarPoint(point.getRadius(), angle);
        notify("Angle", oldAngle, angle);
        notify("Width", getWidth(), getWidth());
        notify("Height", getHeight(), getHeight());
    }

    @Override
    public void rotate(int units) {
        setAngle(point.getAngle() + units * UNIT);
    }

    @Override
    public void move(int deltaX, int deltaY) {
        int height = getHeight();
        int width = getWidth();
        setX(x + deltaX);
        setY(y + deltaY);
        notify("Width", width, getWidth());
        notify("Height", height, getHeight());
    }

    // Abstract method that subclasses must implement for property change notification
    @Visible(false)
    public abstract void notify(String property, Object old, Object current);
}

//END OF FILE
//START OF FILE: mp/shapes/ALocatable.java
package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.PropertyNames;
import util.annotations.EditablePropertyNames;
import tags301.Comp301Tags;

@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
@PropertyNames({"X", "Y"})
@EditablePropertyNames({"X", "Y"})
public class ALocatable implements Locatable {
    protected int x, y;
    
    public ALocatable() {
        this.x = 0;
        this.y = 0;
    }
    
    public ALocatable(int x, int y) {
        this.x = x;
        this.y = y;
    }
    
    @Override
    public int getX() {
        return x;
    }
    
    @Override
    public void setX(int x) {
        this.x = x;
    }
    
    @Override
    public int getY() {
        return y;
    }
    
    @Override
    public void setY(int y) {
        this.y = y;
    }
}

//END OF FILE
//START OF FILE: mp/shapes/APolarPoint.java
package mp.shapes;

public class APolarPoint extends AbstractPolarPoint{
	public APolarPoint(double theRadius, double theAngle) {
		super(theRadius, theAngle);
	}
	public APolarPoint(int theX, int theY) {
		super(theX, theY);
	}
}

//END OF FILE
//START OF FILE: mp/shapes/AScaleRectangle.java
package mp.shapes;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.PropertyNames;
import util.annotations.EditablePropertyNames;
import tags301.Comp301Tags;
@Tags({Comp301Tags.BOUNDED_SHAPE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
@PropertyNames({"X", "Y", "Width", "Height"})
@EditablePropertyNames({"X", "Y", "Width", "Height"})
public class AScaleRectangle extends AbstractAScaleRectangle{
	public AScaleRectangle(int a, int b, int w, int h) {
		super(a, b, w, h);
	}
}
//END OF FILE
//START OF FILE: mp/shapes/AScaleRectangleInterface.java
package mp.shapes;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface AScaleRectangleInterface extends BoundedShape{
	public void scale(int percentage);
	public void setHeight(int x);
	public void setWidth(int x);
}

//END OF FILE
//START OF FILE: mp/shapes/BoundedShape.java
package mp.shapes;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface BoundedShape extends Locatable{
    int getWidth();
    int getHeight();
    void setWidth(int x);
    void setHeight(int x);
}
//END OF FILE
//START OF FILE: mp/shapes/ConsoleScene.java
package mp.shapes;

import java.beans.PropertyChangeEvent;
import java.util.List;
import java.util.ArrayList;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.models.PropertyListenerRegisterer;
import mp.bridge.BridgeScene;
import mp.bridge.Avatar;
import tags301.Comp301Tags;

@Tags({Comp301Tags.CONSOLE_SCENE_VIEW})
@StructurePattern(StructurePatternNames.BEAN_PATTERN)
public class ConsoleScene implements ConsoleSceneInterface{
	private static ConsoleSceneInterface instance;
	private List<PropertyChangeEvent> propertyChangeEvents = new ArrayList<>();

	@Override
	public void propertyChange(PropertyChangeEvent evt) {
		// Store the event for test framework to count
		propertyChangeEvents.add(evt);
		System.out.println("Property changed: " + evt.getPropertyName() +
                          " from " + evt.getOldValue() +
                          " to " + evt.getNewValue() +
                          " on " + evt.getSource().getClass().getSimpleName());
	}

	// Method for test framework to access events
	public List<PropertyChangeEvent> getPropertyChangeEvents() {
		return propertyChangeEvents;
	}

	// Method to clear events (for testing)
	public void clearPropertyChangeEvents() {
		propertyChangeEvents.clear();
	}

	public static ConsoleSceneInterface consoleSceneViewFactoryMethod() {
	    if (instance == null) {
	      instance = new ConsoleScene();
	    }
	    return instance;
	}

	public static void registerWithBridgeScene(BridgeScene bridgeScene, ConsoleSceneInterface consoleScene) {
        // Register as observer for all atomic shapes in the scene
        registerWithAvatar(bridgeScene.getArthur(), consoleScene);
        registerWithAvatar(bridgeScene.getLancelot(), consoleScene);
        registerWithAvatar(bridgeScene.getRobin(), consoleScene);
        registerWithAvatar(bridgeScene.getGalahad(), consoleScene);
        registerWithAvatar(bridgeScene.getGuard(), consoleScene);

        // Register with standing areas
        if (bridgeScene.getKnightArea() instanceof PropertyListenerRegisterer) {
            ((PropertyListenerRegisterer) bridgeScene.getKnightArea()).addPropertyChangeListener(consoleScene);
        }
        if (bridgeScene.getGuardArea() instanceof PropertyListenerRegisterer) {
            ((PropertyListenerRegisterer) bridgeScene.getGuardArea()).addPropertyChangeListener(consoleScene);
        }
    }

    private static void registerWithAvatar(Avatar avatar, ConsoleSceneInterface consoleScene) {
        // Register with head
        if (avatar.getHead() instanceof PropertyListenerRegisterer) {
            ((PropertyListenerRegisterer) avatar.getHead()).addPropertyChangeListener(consoleScene);
        }

        // Register with speech bubble
        if (avatar.getStringShape() instanceof PropertyListenerRegisterer) {
            ((PropertyListenerRegisterer) avatar.getStringShape()).addPropertyChangeListener(consoleScene);
        }

        // Register with arms and legs (composite shapes)
        registerWithCompositeShape(avatar.getArms(), consoleScene);
        registerWithCompositeShape(avatar.getLegs(), consoleScene);
    }

    private static void registerWithCompositeShape(Object shape, ConsoleSceneInterface consoleScene) {
        // For composite shapes like Shape (arms/legs), we need to register with their components
        if (shape instanceof mp.bridge.Shape) {
            mp.bridge.Shape compositeShape = (mp.bridge.Shape) shape;
            if (compositeShape.getLeftLine() instanceof PropertyListenerRegisterer) {
                ((PropertyListenerRegisterer) compositeShape.getLeftLine()).addPropertyChangeListener(consoleScene);
            }
            if (compositeShape.getRightLine() instanceof PropertyListenerRegisterer) {
                ((PropertyListenerRegisterer) compositeShape.getRightLine()).addPropertyChangeListener(consoleScene);
            }
        }
    }
}

//END OF FILE
//START OF FILE: mp/shapes/ConsoleSceneInterface.java
package mp.shapes;
import java.beans.PropertyChangeListener;

import tags301.Comp301Tags;
import util.annotations.Tags;
@Tags(Comp301Tags.CONSOLE_SCENE_VIEW)
public interface ConsoleSceneInterface extends PropertyChangeListener{

}

//END OF FILE
//START OF FILE: mp/shapes/Get.java
package mp.shapes;

public interface Get {
 public RotateLine getLeftLine();
 public RotateLine getRightLine();
 
}

//END OF FILE
//START OF FILE: mp/shapes/GetRectangle.java
package mp.shapes;

public interface GetRectangle extends Get{
	public AScaleRectangleInterface getRectangle();
}

//END OF FILE
//START OF FILE: mp/shapes/Gorge.java
package mp.shapes;

public class Gorge implements GetRectangle{
    int start = 950;
    int top = 0;
    int height = 1000;
    int num1 = 0;
    int num2 = -1000;
    int upper = 400;
    int lower = 100;
    RotateLine leftLine;
    RotateLine rightLine;
    AScaleRectangleInterface rectangle;
    public Gorge(final int x) {
        leftLine = new RotatingLine();
        leftLine.setRadius(height);
        leftLine.setAngle((Math.PI/2));
        leftLine.setX(x);
        leftLine.setY(top);
        leftLine.move(num1, num2);
        
        rightLine = new RotatingLine();
        rightLine.setRadius(height);
        rightLine.setAngle(Math.PI/2);
        rightLine.setX(start);
        rightLine.setY(top);
        rightLine.move(num1, num2);
        
        rectangle = new AScaleRectangle(x, upper, start - x, lower);
    }
    @Override
    public RotateLine getLeftLine() {
    	return leftLine;
    }
    @Override
    public RotateLine getRightLine(){
    	return rightLine;
    }
    @Override
    public AScaleRectangleInterface getRectangle() {
    	return rectangle;
    }
}
//END OF FILE
//START OF FILE: mp/shapes/Locatable.java
package mp.shapes;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface Locatable {
    int getX();
    void setX(final int x);
    int getY();
    void setY(final int y);
}

//END OF FILE
//START OF FILE: mp/shapes/Moveable.java
package mp.shapes;

public interface Moveable {
public void move(int deltaX, int deltaY);
}

//END OF FILE
//START OF FILE: mp/shapes/PolarPointInterface.java
package mp.shapes;

public interface PolarPointInterface extends Locatable{
	public double getAngle();
	public double getRadius();
	public void setAngle(double angle);
	public void setRadius(double radius);
}

//END OF FILE
//START OF FILE: mp/shapes/RotateLine.java
package mp.shapes;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public interface RotateLine extends BoundedShape, Moveable, PolarPointInterface{
    void setRadius(double radius);
    void setAngle(double angle);
    void rotate(int units);
    int getHeight();
    public void notify(String property, Object old, Object current);
}
//END OF FILE
//START OF FILE: mp/shapes/RotatingLine.java
package mp.shapes;
import util.annotations.Visible;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.PropertyNames;
import util.annotations.EditablePropertyNames;
import tags301.Comp301Tags;
import util.models.PropertyListenerRegisterer;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.List;
import java.util.ArrayList;
@Tags({Comp301Tags.ROTATING_LINE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
@PropertyNames({"X", "Y", "Width", "Height", "Radius", "Angle"})
@EditablePropertyNames({"X", "Y", "Width", "Height", "Radius", "Angle"})
public class RotatingLine extends AbstractRotateLine implements PropertyListenerRegisterer{
    @Visible(false)
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    @Override
	public void addPropertyChangeListener(PropertyChangeListener arg0) {
		propertyChangeListeners.add(arg0);
	}

    @Override
    public void notify(String property, Object old, Object current) {
        PropertyChangeEvent event = new PropertyChangeEvent(this, property, old, current);
        for (PropertyChangeListener some : propertyChangeListeners) {some.propertyChange(event);}
    }
}

//END OF FILE
