package main;

import bus.uigen.OEFrame;
import bus.uigen.ObjectEditor;
import mp.bridge.BridgeScene;
import mp.shapes.ConsoleScene;
import mp.shapes.ConsoleSceneInterface;

public class Assignment2 {
    public static final long SLEEP_SECONDS = 50;
    public static void main(String[] args) throws InterruptedException {
    	final BridgeScene scene = StaticFactoryClass.bridgeSceneFactoryMethod();
        final OEFrame frame = ObjectEditor.edit(scene);

        // Create and register console scene observer using factory method
        final ConsoleSceneInterface consoleScene = StaticFactoryClass.consoleSceneViewFactoryMethod();
        ConsoleScene.registerWithBridgeScene(scene, consoleScene);

        // Demo the scene methods - no need for manual refresh calls
        scene.approach(scene.getArthur());
        Thread.sleep(SLEEP_SECONDS);
        scene.say("What is your name?");
        Thread.sleep(SLEEP_SECONDS);
        scene.say("<PERSON>, King of the Britons");
        Thread.sleep(SLEEP_SECONDS);
        scene.say("What is your quest?");
        Thread.sleep(SLEEP_SECONDS);
        scene.say("To seek the Holy Grail");
        Thread.sleep(SLEEP_SECONDS);
        scene.passed();
        Thread.sleep(SLEEP_SECONDS);

        scene.approach(scene.getLancelot());
        Thread.sleep(SLEEP_SECONDS);
        scene.say("What is your favorite color?");
        Thread.sleep(SLEEP_SECONDS);
        scene.say("Blue");
        Thread.sleep(SLEEP_SECONDS);
        scene.failed();
    }
}
