package mp.shapes;
import util.annotations.Visible;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.PropertyNames;
import util.annotations.EditablePropertyNames;
import tags301.Comp301Tags;
import util.models.PropertyListenerRegisterer;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.List;
import java.util.ArrayList;
@StructurePattern(StructurePatternNames.LINE_PATTERN)
@PropertyNames({"X", "Y", "Width", "Height", "Radius", "Angle"})
@EditablePropertyNames({"X", "Y", "Width", "Height", "Radius", "Angle"})
public class RotatingLine extends AbstractRotateLine implements PropertyListenerRegisterer{
    @Visible(false)
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();

    @Override
	public void addPropertyChangeListener(PropertyChangeListener arg0) {
		propertyChangeListeners.add(arg0);
	}

    @Override
    public void notify(String property, Object old, Object current) {
        PropertyChangeEvent event = new PropertyChangeEvent(this, property, old, current);
        for (PropertyChangeListener some : propertyChangeListeners) {some.propertyChange(event);}
    }
}
