Starting audit...
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component args in Identifier args is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component console in Identifier consoleScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component frame in Identifier frame is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component scene in Identifier consoleScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component scene in Identifier scene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component seconds in Identifier SLEEP_SECONDS is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component sleep in Identifier SLEEP_SECONDS is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Expected type names/tags: [main.Assignment1, @Comp301Tags.ROTATING_LINE, @Comp301Tags.ANGLE, @Comp301Tags.AVATAR, @Comp301Tags.BRIDGE_SCENE, main.Assignment2, @Comp301Tags.LOCATABLE, @Comp301Tags.BOUNDED_SHAPE, @Comp301Tags.FACTORY_CLASS, @Comp301Tags.CONSOLE_SCENE_VIEW, main.Assignment3, @Comp301Tags.INHERITING_BRIDGE_SCENE_PAINTER, @Comp301Tags.PAINT_LISTENER, @Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER, @Comp301Tags.BRIDGE_SCENE_CONTROLLER, @Comp301Tags.TABLE, @Comp301Tags. PROGRESS_BAR_CREATOR] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:1: Method:main:String[]->void in class main.Assignment2:[main.Assignment2] has made expected call (.*)!sleep:long->void. Good! [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:1: Method:main:String[]->void in class main.Assignment2:[main.Assignment2] has made expected call @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE. Good! [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:1: Method:main:String[]->void in class main.Assignment2:[main.Assignment2] has made expected call @Comp301Tags.FACTORY_CLASS!@consoleSceneViewFactoryMethod:->.*. Good! [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Access modifiers used: Access Modifiers Used: [(main.Assignment2, public, private, 3, main.Assignment2, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:1 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:1 Public Variables Fraction:1.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:0.0 Average Variable Access:0.0 Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:9.0 Average Local References per Variable:9.0 Average Local Assignments per Variable:9.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Component assignment in Identifier main.Assignment2 is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Component main in Identifier main.Assignment2 is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[static public  main:String[]->void]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Type main.Assignment2 matches tags (main.Assignment2)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:10:30: Named Constant SLEEP_SECONDS defined. Good! [ConstantDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:11:29: Parameter args should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:12: Interface BridgeScene used as the type of variable/function scene. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:16: Interface ConsoleSceneInterface used as the type of variable/function consoleScene. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component area in Identifier AREA_AX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component area in Identifier AREA_HEIGHT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component area in Identifier AREA_WIDTH is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component area in Identifier guardArea is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component area in Identifier knightArea is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component arthur in Identifier arthur is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component avatar in Identifier avatar is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component ax in Identifier AREA_AX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component ax in Identifier GORGE_AX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component ax in Identifier SOME_AX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component ay in Identifier GUARD_AY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component ay in Identifier KNIGHT_AY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component ay in Identifier SOME_AY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component constant in Identifier CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component constant in Identifier GAL_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component constant in Identifier GUARD_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component constant in Identifier LANCELOT_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component constant in Identifier ROBIN_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component cur in Identifier cur is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component gal in Identifier GAL_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component galahad in Identifier galahad is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component gorge in Identifier GORGE_AX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component gorge in Identifier gorge is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component guard in Identifier GUARD_AY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component guard in Identifier GUARD_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component guard in Identifier guard is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component guard in Identifier guardArea is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component height in Identifier AREA_HEIGHT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component knight in Identifier KNIGHT_AY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component knight in Identifier knightArea is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component knight in Identifier knightTurn is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component lancelot in Identifier LANCELOT_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component lancelot in Identifier lancelot is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component number in Identifier number is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component occupied in Identifier occupied is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component right in Identifier RIGHT_SIDE_X is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component robin in Identifier ROBIN_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component robin in Identifier robin is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component say in Identifier say is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component side in Identifier RIGHT_SIDE_X is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component some in Identifier SOME_AX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component some in Identifier SOME_AY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component turn in Identifier knightTurn is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component width in Identifier AREA_WIDTH is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component x in Identifier RIGHT_SIDE_X is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected getter for property Arthur of type @Comp301Tags.AVATAR in parent type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected getter for property Galahad of type @Comp301Tags.AVATAR in parent type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected getter for property Guard of type @Comp301Tags.AVATAR in parent type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected getter for property Lancelot of type @Comp301Tags.AVATAR in parent type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected getter for property Robin of type @Comp301Tags.AVATAR in parent type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected instantiation of @Comp301Tags.AVATAR in type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE] by methods [public  BridgeSceneImpl:->]. Good! [ExpectedClassInstantiations]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: The following public methods do not override: [public  scroll:int;int->void] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Access modifiers used: Access Modifiers Used: [(main.BridgeSceneImpl, public, private, 3, main.BridgeSceneImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:1 Number of Methods:16 Number of Functions:11 Number of Non Getter Functions:0 Number of Getters and Setters:11 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:26 Public Variables Fraction:0.23076923076923078 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:0.7692307692307693 Average Variable Access:2.307692307692308 Number of Properties:11 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:2.789473684210526 Average Local References per Variable:3.923076923076923 Average Local Assignments per Variable:3.923076923076923 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Component bridge in Identifier main.BridgeSceneImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Component impl in Identifier main.BridgeSceneImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Component main in Identifier main.BridgeSceneImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Component scene in Identifier main.BridgeSceneImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Expected signature @scroll:int;int->void//EC in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Expected signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Expected signature failed:->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Expected signature passed:->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Expected signature say:String->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Interfaces defined: [mp.bridge.BridgeScene]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  passed:->void, public  failed:->void, public  approach:mp.bridge.Avatar->void, public  say:String->void, public  scroll:int;int->void]Getters:[public  getArthur:->mp.bridge.Avatar, public  getLancelot:->mp.bridge.Avatar, public  getRobin:->mp.bridge.Avatar, public  getGalahad:->mp.bridge.Avatar, public  getGuard:->mp.bridge.Avatar, public  getKnightArea:->mp.shapes.AScaleRectangleInterface, public  getGuardArea:->mp.shapes.AScaleRectangleInterface, public  getGorge:->mp.shapes.GetRectangle, public  getOccupied:->boolean, public  getKnightTurn:->boolean, public  getInteractingKnight:->mp.bridge.Avatar]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Properties defined: Properties:[readonly  p-v:3 access:public KnightTurn:boolean(public , null), readonly  p-v:5 access:public InteractingKnight:mp.bridge.Avatar(public , null), readonly  p-v:3 access:public Gorge:mp.shapes.GetRectangle(public , null), readonly  p-v:3 access:public Occupied:boolean(public , null), readonly  p-v:3 access:public Arthur:mp.bridge.Avatar(public , null), readonly  p-v:3 access:public KnightArea:mp.shapes.AScaleRectangleInterface(public , null), readonly  p-v:3 access:public Guard:mp.bridge.Avatar(public , null), readonly  p-v:3 access:public Lancelot:mp.bridge.Avatar(public , null), readonly  p-v:3 access:public GuardArea:mp.shapes.AScaleRectangleInterface(public , null), readonly  p-v:3 access:public Galahad:mp.bridge.Avatar(public , null), readonly  p-v:3 access:public Robin:mp.bridge.Avatar(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Type main.BridgeSceneImpl matches tags (@Comp301Tags.BRIDGE_SCENE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19:1: Class Data Abstraction Coupling is 8 (max allowed is 7) classes [AScaleRectangle, ArthurHead, AvatarImpl, GalahadHead, Gorge, GuardHead, LancelotHead, RobinHead]. [ClassDataAbstractionCoupling]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:22: Interface Avatar used as the type of variable/function arthur. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:22: Interface Avatar used as the type of variable/function galahad. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:22: Interface Avatar used as the type of variable/function guard. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:22: Interface Avatar used as the type of variable/function lancelot. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:22: Interface Avatar used as the type of variable/function robin. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:23:29: Named Constant SOME_AX defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:24:29: Named Constant SOME_AY defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:25:29: Named Constant LANCELOT_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:26:29: Named Constant ROBIN_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:27:29: Named Constant GAL_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:28:29: Named Constant GUARD_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:29: Interface GetRectangle used as the type of variable/function gorge. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:30: Interface Avatar used as the type of variable/function cur. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:31: Interface AScaleRectangleInterface used as the type of variable/function knightArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:32: Interface AScaleRectangleInterface used as the type of variable/function guardArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:34:30: Named Constant AREA_AX defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:35:30: Named Constant KNIGHT_AY defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:36:30: Named Constant GUARD_AY defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:37:30: Named Constant AREA_WIDTH defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:38:30: Named Constant AREA_HEIGHT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:40:30: Named Constant GORGE_AX defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:41:30: Named Constant RIGHT_SIDE_X defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:43:30: Named Constant CONSTANT defined. Good! [ConstantDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:77: In method failed, rewrite if to use else-ifs  rather than nested ifs (that is, use else branching instead of then branching) [ThenBranching]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:78: nestedIfDepth [NestedBlockDepth]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:96: Interface Avatar used as the type of variable/function avatar. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:96:26: Final parameter avatar defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:105:21: Final parameter say defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:121: Interface Avatar used as the type of variable/function getArthur. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:125: Interface Avatar used as the type of variable/function getLancelot. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:129: Interface Avatar used as the type of variable/function getRobin. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:133: Interface Avatar used as the type of variable/function getGalahad. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:137: Interface Avatar used as the type of variable/function getGuard. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:141: Interface AScaleRectangleInterface used as the type of variable/function getKnightArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:145: Interface AScaleRectangleInterface used as the type of variable/function getGuardArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:149: Interface GetRectangle used as the type of variable/function getGorge. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:157: Interface Avatar used as the type of variable/function getInteractingKnight. Good! [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:161:24: Parameter deltaX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:161:36: Parameter deltaY should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component args in Identifier args is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component max in Identifier MAX_PRINTED_TRACES is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component max in Identifier MAX_TRACES is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component printed in Identifier MAX_PRINTED_TRACES is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component process in Identifier PROCESS_TIME is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component time in Identifier PROCESS_TIME is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component traces in Identifier MAX_PRINTED_TRACES is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component traces in Identifier MAX_TRACES is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Access modifiers used: Access Modifiers Used: [(main.RunSS25A2Tests, public, private, 3, main.RunSS25A2Tests, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:1 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:1.0 Average Local References per Variable:1.0 Average Local Assignments per Variable:1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Component a in Identifier main.RunSS25A2Tests is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Component main in Identifier main.RunSS25A2Tests is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Component run in Identifier main.RunSS25A2Tests is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Component tests in Identifier main.RunSS25A2Tests is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[static public  main:String[]->void]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:7:35: Named Constant MAX_PRINTED_TRACES defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:8:43: Named Constant MAX_TRACES defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:9:43: Named Constant PROCESS_TIME defined. Good! [ConstantDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:10:41: Parameter args should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:0: Component angle in Identifier leftAngle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:0: Component angle in Identifier rightAngle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:0: Component console in Identifier consoleView is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:0: Component left in Identifier leftAngle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:0: Component left in Identifier leftRadius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:0: Component radius in Identifier leftRadius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:0: Component radius in Identifier rightRadius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:0: Component right in Identifier rightAngle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:0: Component right in Identifier rightRadius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:0: Component scene in Identifier scene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:0: Component view in Identifier consoleView is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Access modifiers used: Access Modifiers Used: [(main.StaticFactoryClass, public, private, 3, main.StaticFactoryClass, null ), (main.StaticFactoryClass, public, package, 2, main.Assignment2, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:4 Number of Functions:4 Number of Non Getter Functions:4 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:2 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.5 Private  Variable Fraction:0.5 Average Variable Access:2.5 Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:NaN Average Local References per Variable:3.0 Average Local Assignments per Variable:3.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Component class in Identifier main.StaticFactoryClass is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Component factory in Identifier main.StaticFactoryClass is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Component main in Identifier main.StaticFactoryClass is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Component static in Identifier main.StaticFactoryClass is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Expected signature @bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Expected signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Expected signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Methods defined: NonGetterFunctions:[static public  bridgeSceneFactoryMethod:->mp.bridge.BridgeScene, static public  consoleSceneViewFactoryMethod:->mp.shapes.ConsoleSceneInterface, static public  legsFactoryMethod:->mp.bridge.Angle, static public  legsFactoryMethod:double;double;double;double->mp.bridge.Angle]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Missing signature @bridgeSceneControllerFactoryMethod:->@Comp301Tags.BRIDGE_SCENE_CONTROLLER in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Missing signature @delegatingBridgeSceneViewFactoryMethod:->@Comp301Tags.DELEGATING_BRIDGE_SCENE_VIEW in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Missing signature @inheritingBridgeScenePainterFactoryMethod:->@Comp301Tags.INHERITING_BRIDGE_SCENE_PAINTER in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Missing signature @observableBridgeScenePainterFactoryMethod:->@Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Type main.StaticFactoryClass matches tags (@Comp301Tags.FACTORY_CLASS)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:11: Interface BridgeScene used as the type of variable/function scene. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:12: Interface ConsoleSceneInterface used as the type of variable/function consoleView. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:14: Interface BridgeScene used as the type of variable/function bridgeSceneFactoryMethod. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:19: Interface ConsoleSceneInterface used as the type of variable/function consoleSceneViewFactoryMethod. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:26: Interface Angle used as the type of variable/function legsFactoryMethod. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:31: Interface Angle used as the type of variable/function legsFactoryMethod. Good! [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:31:39: Parameter leftRadius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:31:58: Parameter leftAngle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:31:76: Parameter rightRadius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:31:96: Parameter rightAngle should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component change in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component current in Identifier current is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component event in Identifier event is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component file in Identifier file is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component file in Identifier imageFileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component file in Identifier oldFileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component image in Identifier imageFileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component listener in Identifier listener is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component listeners in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component name in Identifier imageFileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component name in Identifier oldFileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component old in Identifier old is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component old in Identifier oldFileName is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component old in Identifier oldX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component old in Identifier oldY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component property in Identifier property is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component property in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component x in Identifier oldX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component y in Identifier oldY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Class mp.bridge.AbstractImageShape with instance methods does not have exactly one interface.       [ClassHasOneInterface]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Consider separating the following method sets into different types [[public  getImageFileName:->String, public  setImageFileName:String->void, public  getX:->int, public  setX:int->void, public  getY:->int, public  setY:int->void], [public  notify:String;Object;Object->void, public  addPropertyChangeListener:java.beans.PropertyChangeListener->void]] [ClassDecomposition]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Expected instantiation of java.beans.PropertyChangeEvent in type mp.bridge.AbstractImageShape[@Comp301Tags.BOUNDED_SHAPE] by methods [public  notify:String;Object;Object->void]. Good! [ExpectedClassInstantiations]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Missing getter for property Height of type int in parent type mp.bridge.AbstractImageShape[@Comp301Tags.BOUNDED_SHAPE] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Missing getter for property Width of type int in parent type mp.bridge.AbstractImageShape[@Comp301Tags.BOUNDED_SHAPE] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Missing setter for property Height of type int in parent type mp.bridge.AbstractImageShape[@Comp301Tags.BOUNDED_SHAPE]. [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Missing setter for property Width of type int in parent type mp.bridge.AbstractImageShape[@Comp301Tags.BOUNDED_SHAPE]. [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractImageShape and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractImageShape and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractImageShape and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractImageShape and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractImageShape and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractImageShape and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractImageShape and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractImageShape and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractImageShape and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractImageShape and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractImageShape and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractImageShape and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractImageShape and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractImageShape and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractImageShape and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractImageShape and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractImageShape and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractImageShape and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractImageShape and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractImageShape and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractImageShape and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractImageShape and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: Some method (setImageFileName:String->void) in class mp.bridge.AbstractImageShape:[@Comp301Tags.BOUNDED_SHAPE] has made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:1: The following public methods do not override: [public  notify:String;Object;Object->void] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:14: Access modifiers used: Access Modifiers Used: [(mp.bridge.AbstractImageShape, public, private, 3, mp.bridge.AbstractImageShape, null ), (mp.bridge.AbstractImageShape, public, public, 0, main.Assignment2, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:14: Aggregate statistics:  Is Abstract:true Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:1 Number of Methods:8 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:4 Public Variables Fraction:0.0 Protected Variables Fraction:0.75 Package Access Variables Fraction:0.0 Private  Variable Fraction:0.25 Average Variable Access:1.5 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:2.0 Average Local References per Variable:5.0 Average Local Assignments per Variable:5.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:14: Component abstract in Identifier mp.bridge.AbstractImageShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:14: Component bridge in Identifier mp.bridge.AbstractImageShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:14: Component image in Identifier mp.bridge.AbstractImageShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:14: Component shape in Identifier mp.bridge.AbstractImageShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:14: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:14: Interfaces defined: [mp.bridge.ImageShape, util.models.PropertyListenerRegisterer]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:14: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  notify:String;Object;Object->void]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:14: Missing supertype: @Comp301Tags.LOCATABLE of type mp.bridge.AbstractImageShape[@Comp301Tags.BOUNDED_SHAPE] [ExpectedSuperTypes]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:14: Properties defined: Properties:[editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:1 access:public X:int(public ,public ), editable, g-s:0 p-v:1 access:public Y:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:14: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:14: Type mp.bridge.AbstractImageShape matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:14: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:21:31: Parameter defaultImageFileName should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:33:34: Parameter file should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:34:16: Variable 'oldFileName' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:39: Signatures public  getX:->int common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:39: Signatures public  getX:->int common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:44: Signatures public  setX:int->void common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:44: Signatures public  setX:int->void common with mp.shapes.AbstractPolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:44: Signatures public  setX:int->void common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:45:22: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:45:26: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:46:13: Variable 'oldX' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:51: Signatures public  getY:->int common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:51: Signatures public  getY:->int common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:56: Signatures public  setY:int->void common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:56: Signatures public  setY:int->void common with mp.shapes.AbstractPolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:56: Signatures public  setY:int->void common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:57:22: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:57:26: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:58:13: Variable 'oldY' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:65: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:66:43: Parameter listener should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:70: Signatures public  notify:String;Object;Object->void common with mp.shapes.AbstractAScaleRectangle not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:70: Signatures public  notify:String;Object;Object->void common with mp.shapes.AbstractRotateLine not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:71:24: Parameter property should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:71:41: Parameter old should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:71:53: Parameter current should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:72:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component change in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component current in Identifier current is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component event in Identifier event is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component listener in Identifier listener is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component listeners in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component old in Identifier old is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component old in Identifier oldText is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component old in Identifier oldX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component old in Identifier oldY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component property in Identifier property is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component property in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component text in Identifier oldText is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component text in Identifier text is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component x in Identifier oldX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component y in Identifier oldY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Class mp.bridge.AbstractStringShape with instance methods does not have exactly one interface.       [ClassHasOneInterface]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Consider separating the following method sets into different types [[public  setX:int->void, public  getText:->String, public  getY:->int, public  getX:->int, public  setText:String->void, public  setY:int->void], [public  notify:String;Object;Object->void, public  addPropertyChangeListener:java.beans.PropertyChangeListener->void]] [ClassDecomposition]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractStringShape and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractStringShape and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractStringShape and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractStringShape and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractStringShape and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractStringShape and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractStringShape and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractStringShape and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractStringShape and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractStringShape and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractStringShape and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractStringShape and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractStringShape and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractStringShape and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractStringShape and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractStringShape and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractStringShape and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractStringShape and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractStringShape and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractStringShape and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractStringShape and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractStringShape and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractStringShape and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractStringShape and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractStringShape and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractStringShape and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractStringShape and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractStringShape and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractStringShape and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractStringShape and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:1: The following public methods do not override: [public  notify:String;Object;Object->void] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:10: Access modifiers used: Access Modifiers Used: [(mp.bridge.AbstractStringShape, public, private, 3, mp.bridge.AbstractStringShape, null ), (mp.bridge.AbstractStringShape, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.AbstractStringShape, public, public, 0, main.Assignment2, null ), (mp.bridge.AbstractStringShape, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.AbstractStringShape, public, public, 0, mp.shapes.AbstractRotateLine, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:10: Aggregate statistics:  Is Abstract:true Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:1 Number of Methods:8 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:4 Public Variables Fraction:0.0 Protected Variables Fraction:0.75 Package Access Variables Fraction:0.0 Private  Variable Fraction:0.25 Average Variable Access:1.5 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:2.0 Average Local References per Variable:5.75 Average Local Assignments per Variable:5.75 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:10: Component abstract in Identifier mp.bridge.AbstractStringShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:10: Component bridge in Identifier mp.bridge.AbstractStringShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:10: Component shape in Identifier mp.bridge.AbstractStringShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:10: Component string in Identifier mp.bridge.AbstractStringShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:10: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:10: Interfaces defined: [mp.bridge.StringShape, util.models.PropertyListenerRegisterer]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:10: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  notify:String;Object;Object->void]Getters:[public  getText:->String, public  getX:->int, public  getY:->int]Setters:[public  setText:String->void, public  setX:int->void, public  setY:int->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:10: Properties defined: Properties:[editable, g-s:0 p-v:1 access:public X:int(public ,public ), editable, g-s:0 p-v:1 access:public Y:int(public ,public ), editable, g-s:0 p-v:1 access:public Text:String(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:10: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:10: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:10: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:15:32: Parameter defaultText should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:27:25: Parameter text should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:27:32: 'text' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:28:16: Variable 'oldText' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:33: Signatures public  getX:->int common with mp.bridge.AbstractImageShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:33: Signatures public  getX:->int common with mp.shapes.ALocatable defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:33: Signatures public  getX:->int common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:33: Signatures public  getX:->int common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:38: Signatures public  setX:int->void common with mp.bridge.AbstractImageShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:38: Signatures public  setX:int->void common with mp.shapes.ALocatable defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:38: Signatures public  setX:int->void common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:38: Signatures public  setX:int->void common with mp.shapes.AbstractPolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:38: Signatures public  setX:int->void common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:39:22: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:39:26: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:40:13: Variable 'oldX' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:45: Signatures public  getY:->int common with mp.bridge.AbstractImageShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:45: Signatures public  getY:->int common with mp.shapes.ALocatable defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:45: Signatures public  getY:->int common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:45: Signatures public  getY:->int common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:50: Signatures public  setY:int->void common with mp.bridge.AbstractImageShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:50: Signatures public  setY:int->void common with mp.shapes.ALocatable defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:50: Signatures public  setY:int->void common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:50: Signatures public  setY:int->void common with mp.shapes.AbstractPolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:50: Signatures public  setY:int->void common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:51:22: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:51:26: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:52:13: Variable 'oldY' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:57: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.AbstractImageShape defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:57: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:57: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.RotatingLine defined in common types [util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:58:43: Parameter listener should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:62: Signatures public  notify:String;Object;Object->void common with mp.bridge.AbstractImageShape not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:62: Signatures public  notify:String;Object;Object->void common with mp.shapes.AbstractAScaleRectangle not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:62: Signatures public  notify:String;Object;Object->void common with mp.shapes.AbstractRotateLine not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:62: Signatures public  notify:String;Object;Object->void common with mp.shapes.RotatingLine not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:63:24: Parameter property should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:63:41: Parameter old should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:63:53: Parameter current should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:64:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:0 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:NaN Protected Methods Fraction:NaN Package Access Methods Fraction:NaN Private  Methods Fraction:NaN Average Method Access:NaN Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Component angle in Identifier mp.bridge.Angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Component bridge in Identifier mp.bridge.Angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Super types: [mp.shapes.Moveable, mp.shapes.Get] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Type mp.bridge.Angle matches tags (@Comp301Tags.ANGLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Missing getter for property Arms of type @Comp301Tags.ANGLE in parent type mp.bridge.ArthurHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Missing getter for property Head of type @IMAGE_PATTERN in parent type mp.bridge.ArthurHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Missing getter for property Legs of type @Comp301Tags.ANGLE in parent type mp.bridge.ArthurHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Missing getter for property StringShape of type @STRING_PATTERN in parent type mp.bridge.ArthurHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between ArthurHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between ArthurHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between ArthurHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between ArthurHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ArthurHead and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ArthurHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ArthurHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ArthurHead and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ArthurHead and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ArthurHead and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ArthurHead and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ArthurHead and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ArthurHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ArthurHead and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ArthurHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ArthurHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ArthurHead and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ArthurHead and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ArthurHead and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ArthurHead and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ArthurHead and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ArthurHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:9: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:9: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:0 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:NaN Protected Methods Fraction:NaN Package Access Methods Fraction:NaN Private  Methods Fraction:NaN Average Method Access:NaN Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:9: Component arthur in Identifier mp.bridge.ArthurHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:9: Component bridge in Identifier mp.bridge.ArthurHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:9: Component head in Identifier mp.bridge.ArthurHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:9: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:9: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:9: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:9: Missing signature move:int;int->void in type mp.bridge.ArthurHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:9: Missing signature scale:double->void//EC in type mp.bridge.ArthurHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:9: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:9: Super types: mp.bridge.AbstractImageShape [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:9: Type mp.bridge.ArthurHead matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:9: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Access modifiers used: Access Modifiers Used: [(mp.bridge.Avatar, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.Avatar, public, public, 0, main.Assignment2, null ), (mp.bridge.Avatar, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.Avatar, public, public, 0, mp.shapes.ConsoleScene, null ), (mp.bridge.Avatar, public, package, 2, mp.bridge.Shape, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:5 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:5 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:4 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Component avatar in Identifier mp.bridge.Avatar is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Component bridge in Identifier mp.bridge.Avatar is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[default move:int;int->void]Getters:[default getHead:->mp.bridge.ImageShape, default getStringShape:->mp.bridge.StringShape, default getArms:->mp.bridge.Angle, default getLegs:->mp.bridge.Angle]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Properties defined: Properties:[readonly  p-v:5 access:package Legs:mp.bridge.Angle(default , null), readonly  p-v:5 access:package Head:mp.bridge.ImageShape(default , null), readonly  p-v:5 access:package StringShape:mp.bridge.StringShape(default , null), readonly  p-v:5 access:package Arms:mp.bridge.Angle(default , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Super types: [mp.shapes.Moveable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Type mp.bridge.Avatar matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:11: Interface ImageShape used as the type of variable/function getHead. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:12: Interface StringShape used as the type of variable/function getStringShape. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:13: Interface Angle used as the type of variable/function getArms. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:14: Interface Angle used as the type of variable/function getLegs. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component arms in Identifier arms is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component head in Identifier head is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component legs in Identifier legs is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component speech in Identifier speech is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: All public methods override. Good!  [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Expected getter for property Arms of type @Comp301Tags.ANGLE in parent type mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Expected getter for property Head of type @IMAGE_PATTERN in parent type mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Expected getter for property Legs of type @Comp301Tags.ANGLE in parent type mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]. Good! [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Missing getter for property StringShape of type @STRING_PATTERN in parent type mp.bridge.AvatarImpl[@Comp301Tags.AVATAR] [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Some method (AvatarImpl:mp.bridge.ImageShape->) in class mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR] has made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:7:8: Unused import - mp.shapes.Locatable. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:8:8: Unused import - mp.shapes.Moveable. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Access modifiers used: Access Modifiers Used: [(mp.bridge.AvatarImpl, public, private, 3, mp.bridge.AvatarImpl, null ), (mp.bridge.AvatarImpl, public, public, 0, main.Assignment2, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:1 Public Methods Fraction:0.8333333333333334 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.16666666666666666 Average Method Access:0.5 Number of Variables:4 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:4 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:4.5 Average Local References per Variable:4.5 Average Local Assignments per Variable:4.5 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Component avatar in Identifier mp.bridge.AvatarImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Component bridge in Identifier mp.bridge.AvatarImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Component impl in Identifier mp.bridge.AvatarImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Expected signature move:int;int->void in type mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Interfaces defined: [mp.bridge.Avatar]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[private  layoutAtOrigin:->void, public  move:int;int->void]Getters:[public  getHead:->mp.bridge.ImageShape, public  getStringShape:->mp.bridge.StringShape, public  getArms:->mp.bridge.Angle, public  getLegs:->mp.bridge.Angle]Setters:[]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Missing signature scale:double->void//EC in type mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Properties defined: Properties:[readonly  p-v:3 access:public Legs:mp.bridge.Angle(public , null), readonly  p-v:3 access:public Head:mp.bridge.ImageShape(public , null), readonly  p-v:3 access:public StringShape:mp.bridge.StringShape(public , null), readonly  p-v:3 access:public Arms:mp.bridge.Angle(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Type mp.bridge.AvatarImpl matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Interface ImageShape used as the type of variable/function head. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:14: Interface StringShape used as the type of variable/function speech. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:15: Interface Angle used as the type of variable/function arms. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:16: Interface Angle used as the type of variable/function legs. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:18: Interface ImageShape used as the type of variable/function h. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:18:23: Final parameter h defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:28: Interface ImageShape used as the type of variable/function getHead. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:32: Interface StringShape used as the type of variable/function getStringShape. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:36: Interface Angle used as the type of variable/function getArms. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:40: Interface Angle used as the type of variable/function getLegs. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:44:22: Final parameter deltaX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:44:40: Final parameter deltaY defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:0: Component avatar in Identifier avatar is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:0: Component say in Identifier say is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Access modifiers used: Access Modifiers Used: [(mp.bridge.BridgeScene, public, public, 0, main.Assignment2, null ), (mp.bridge.BridgeScene, public, public, 0, mp.shapes.ConsoleScene, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:15 Number of Functions:11 Number of Non Getter Functions:0 Number of Getters and Setters:11 Number of Non Public Methods:5 Public Methods Fraction:0.6666666666666666 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.3333333333333333 Private  Methods Fraction:0.0 Average Method Access:0.6666666666666666 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:11 Public Properties Fraction:0.5454545454545454 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.45454545454545453 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.9090909090909091 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Component bridge in Identifier mp.bridge.BridgeScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Component scene in Identifier mp.bridge.BridgeScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  passed:->void, public  failed:->void, public  approach:mp.bridge.Avatar->void, public  say:String->void]Getters:[default getArthur:->mp.bridge.Avatar, default getLancelot:->mp.bridge.Avatar, default getRobin:->mp.bridge.Avatar, default getGalahad:->mp.bridge.Avatar, default getGuard:->mp.bridge.Avatar, public  getKnightArea:->mp.shapes.AScaleRectangleInterface, public  getGuardArea:->mp.shapes.AScaleRectangleInterface, public  getGorge:->mp.shapes.GetRectangle, public  getOccupied:->boolean, public  getKnightTurn:->boolean, public  getInteractingKnight:->mp.bridge.Avatar]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Properties defined: Properties:[readonly  p-v:5 access:public KnightTurn:boolean(public , null), readonly  p-v:5 access:public InteractingKnight:mp.bridge.Avatar(public , null), readonly  p-v:5 access:public Gorge:mp.shapes.GetRectangle(public , null), readonly  p-v:5 access:public Occupied:boolean(public , null), readonly  p-v:5 access:package Arthur:mp.bridge.Avatar(default , null), readonly  p-v:5 access:public KnightArea:mp.shapes.AScaleRectangleInterface(public , null), readonly  p-v:5 access:package Guard:mp.bridge.Avatar(default , null), readonly  p-v:5 access:package Lancelot:mp.bridge.Avatar(default , null), readonly  p-v:5 access:public GuardArea:mp.shapes.AScaleRectangleInterface(public , null), readonly  p-v:5 access:package Galahad:mp.bridge.Avatar(default , null), readonly  p-v:5 access:package Robin:mp.bridge.Avatar(default , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Super types: [] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Type mp.bridge.BridgeScene matches tags (@Comp301Tags.BRIDGE_SCENE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:13: Interface Avatar used as the type of variable/function getArthur. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:14: Interface Avatar used as the type of variable/function getLancelot. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:15: Interface Avatar used as the type of variable/function getRobin. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:16: Interface Avatar used as the type of variable/function getGalahad. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:17: Interface Avatar used as the type of variable/function getGuard. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:20: Interface Avatar used as the type of variable/function avatar. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:22: Interface AScaleRectangleInterface used as the type of variable/function getKnightArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:23: Interface AScaleRectangleInterface used as the type of variable/function getGuardArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:24: Interface GetRectangle used as the type of variable/function getGorge. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:27: Interface Avatar used as the type of variable/function getInteractingKnight. Good! [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Missing getter for property Arms of type @Comp301Tags.ANGLE in parent type mp.bridge.GalahadHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Missing getter for property Head of type @IMAGE_PATTERN in parent type mp.bridge.GalahadHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Missing getter for property Legs of type @Comp301Tags.ANGLE in parent type mp.bridge.GalahadHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Missing getter for property StringShape of type @STRING_PATTERN in parent type mp.bridge.GalahadHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between GalahadHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between GalahadHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between GalahadHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between GalahadHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GalahadHead and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GalahadHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GalahadHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GalahadHead and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GalahadHead and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GalahadHead and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GalahadHead and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GalahadHead and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GalahadHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GalahadHead and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GalahadHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GalahadHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GalahadHead and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GalahadHead and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GalahadHead and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GalahadHead and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GalahadHead and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GalahadHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:8: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:8: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:0 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:NaN Protected Methods Fraction:NaN Package Access Methods Fraction:NaN Private  Methods Fraction:NaN Average Method Access:NaN Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:8: Component bridge in Identifier mp.bridge.GalahadHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:8: Component galahad in Identifier mp.bridge.GalahadHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:8: Component head in Identifier mp.bridge.GalahadHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:8: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:8: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:8: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:8: Missing signature move:int;int->void in type mp.bridge.GalahadHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:8: Missing signature scale:double->void//EC in type mp.bridge.GalahadHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:8: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:8: Super types: mp.bridge.AbstractImageShape [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:8: Type mp.bridge.GalahadHead matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:8: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Missing getter for property Arms of type @Comp301Tags.ANGLE in parent type mp.bridge.GuardHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Missing getter for property Head of type @IMAGE_PATTERN in parent type mp.bridge.GuardHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Missing getter for property Legs of type @Comp301Tags.ANGLE in parent type mp.bridge.GuardHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Missing getter for property StringShape of type @STRING_PATTERN in parent type mp.bridge.GuardHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between GuardHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between GuardHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between GuardHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between GuardHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GuardHead and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GuardHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GuardHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GuardHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GuardHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GuardHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GuardHead and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GuardHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GuardHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GuardHead and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GuardHead and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GuardHead and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GuardHead and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GuardHead and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between GuardHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GuardHead and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GuardHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GuardHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GuardHead and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GuardHead and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GuardHead and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GuardHead and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GuardHead and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between GuardHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:8: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:8: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:0 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:NaN Protected Methods Fraction:NaN Package Access Methods Fraction:NaN Private  Methods Fraction:NaN Average Method Access:NaN Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:8: Component bridge in Identifier mp.bridge.GuardHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:8: Component guard in Identifier mp.bridge.GuardHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:8: Component head in Identifier mp.bridge.GuardHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:8: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:8: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:8: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:8: Missing signature move:int;int->void in type mp.bridge.GuardHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:8: Missing signature scale:double->void//EC in type mp.bridge.GuardHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:8: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:8: Super types: mp.bridge.AbstractImageShape [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:8: Type mp.bridge.GuardHead matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:8: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:0: Component file in Identifier file is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:2 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:1 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Component bridge in Identifier mp.bridge.ImageShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Component image in Identifier mp.bridge.ImageShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Component shape in Identifier mp.bridge.ImageShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getImageFileName:->String]Setters:[default setImageFileName:String->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Properties defined: Properties:[editable, g-s:0 p-v:5 access:package ImageFileName:String(default ,default )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Super types: [mp.shapes.Locatable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Type mp.bridge.ImageShape matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Missing getter for property Arms of type @Comp301Tags.ANGLE in parent type mp.bridge.LancelotHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Missing getter for property Head of type @IMAGE_PATTERN in parent type mp.bridge.LancelotHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Missing getter for property Legs of type @Comp301Tags.ANGLE in parent type mp.bridge.LancelotHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Missing getter for property StringShape of type @STRING_PATTERN in parent type mp.bridge.LancelotHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between LancelotHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between LancelotHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between LancelotHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between LancelotHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between LancelotHead and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between LancelotHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between LancelotHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between LancelotHead and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between LancelotHead and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between LancelotHead and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between LancelotHead and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between LancelotHead and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between LancelotHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.RobinHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between LancelotHead and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between LancelotHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between LancelotHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between LancelotHead and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between LancelotHead and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between LancelotHead and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between LancelotHead and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between LancelotHead and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between LancelotHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:9: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:9: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:0 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:NaN Protected Methods Fraction:NaN Package Access Methods Fraction:NaN Private  Methods Fraction:NaN Average Method Access:NaN Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:9: Component bridge in Identifier mp.bridge.LancelotHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:9: Component head in Identifier mp.bridge.LancelotHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:9: Component lancelot in Identifier mp.bridge.LancelotHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:9: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:9: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:9: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:9: Missing signature move:int;int->void in type mp.bridge.LancelotHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:9: Missing signature scale:double->void//EC in type mp.bridge.LancelotHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:9: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:9: Super types: mp.bridge.AbstractImageShape [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:9: Type mp.bridge.LancelotHead matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:9: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Missing getter for property Arms of type @Comp301Tags.ANGLE in parent type mp.bridge.RobinHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Missing getter for property Head of type @IMAGE_PATTERN in parent type mp.bridge.RobinHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Missing getter for property Legs of type @Comp301Tags.ANGLE in parent type mp.bridge.RobinHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Missing getter for property StringShape of type @STRING_PATTERN in parent type mp.bridge.RobinHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between RobinHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between RobinHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between RobinHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public ImageFileName:String(public ,public ) common between RobinHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RobinHead and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RobinHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RobinHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RobinHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RobinHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RobinHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RobinHead and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RobinHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RobinHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RobinHead and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RobinHead and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RobinHead and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RobinHead and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RobinHead and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RobinHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.ArthurHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.GalahadHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.GuardHead inherited. Good! [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.LancelotHead inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RobinHead and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RobinHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RobinHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RobinHead and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RobinHead and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RobinHead and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RobinHead and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RobinHead and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RobinHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:9: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:9: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:0 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:NaN Protected Methods Fraction:NaN Package Access Methods Fraction:NaN Private  Methods Fraction:NaN Average Method Access:NaN Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:9: Component bridge in Identifier mp.bridge.RobinHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:9: Component head in Identifier mp.bridge.RobinHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:9: Component robin in Identifier mp.bridge.RobinHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:9: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:9: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:9: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:9: Missing signature move:int;int->void in type mp.bridge.RobinHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:9: Missing signature scale:double->void//EC in type mp.bridge.RobinHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:9: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:9: Super types: mp.bridge.AbstractImageShape [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:9: Type mp.bridge.RobinHead matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:9: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:0: Component left in Identifier left is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:0: Component right in Identifier right is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:1: All public methods override. Good!  [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:1: Expected instantiation of @Comp301Tags.ROTATING_LINE in type mp.bridge.Shape[@Comp301Tags.ANGLE] by methods [public  Shape:->, public  Shape:double;double;double;double->]. Good! [ExpectedClassInstantiations]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:1: Missing getter for property LeftLine of type @Comp301Tags.ROTATING_LINE in parent type mp.bridge.Shape[@Comp301Tags.ANGLE] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:1: Missing getter for property RightLine of type @Comp301Tags.ROTATING_LINE in parent type mp.bridge.Shape[@Comp301Tags.ANGLE] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:1: Property readonly  p-v:3 access:public LeftLine:mp.shapes.RotateLine(public , null) common between Shape and mp.shapes.Gorge not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:1: Property readonly  p-v:3 access:public RightLine:mp.shapes.RotateLine(public , null) common between Shape and mp.shapes.Gorge not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:2:8: Unused import - mp.shapes.Get. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Access modifiers used: Access Modifiers Used: [(mp.bridge.Shape, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.Shape, public, public, 0, main.Assignment2, null ), (mp.bridge.Shape, public, public, 0, mp.shapes.ConsoleScene, null ), (mp.bridge.Shape, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.Shape, public, private, 3, mp.bridge.Shape, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:3 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:2 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:6.0 Average Local References per Variable:6.0 Average Local Assignments per Variable:6.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Component bridge in Identifier mp.bridge.Shape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Component shape in Identifier mp.bridge.Shape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Expected signature move:int;int->void in type mp.bridge.Shape:[@Comp301Tags.ANGLE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Interfaces defined: [mp.bridge.Angle]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  move:int;int->void]Getters:[public  getLeftLine:->mp.shapes.RotateLine, public  getRightLine:->mp.shapes.RotateLine]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Properties defined: Properties:[readonly  p-v:3 access:public RightLine:mp.shapes.RotateLine(public , null), readonly  p-v:3 access:public LeftLine:mp.shapes.RotateLine(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Type mp.bridge.Shape matches tags (@Comp301Tags.ANGLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:13: Interface RotateLine used as the type of variable/function left. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:13: Interface RotateLine used as the type of variable/function right. Good! [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:19:18: Parameter leftRadius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:19:37: Parameter leftAngle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:19:55: Parameter rightRadius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:19:75: Parameter rightAngle should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:27: Signatures public  getLeftLine:->mp.shapes.RotateLine common with mp.shapes.Gorge defined in common types [mp.shapes.Get]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:28: Interface RotateLine used as the type of variable/function getLeftLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:31: Signatures public  getRightLine:->mp.shapes.RotateLine common with mp.shapes.Gorge defined in common types [mp.shapes.Get]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:32: Interface RotateLine used as the type of variable/function getRightLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:35: Signatures public  move:int;int->void common with mp.bridge.AvatarImpl defined in common types [mp.shapes.Moveable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:35: Signatures public  move:int;int->void common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Moveable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:36:22: Parameter deltaX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:36:34: Parameter deltaY should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between SpeechBubble and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between SpeechBubble and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between SpeechBubble and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between SpeechBubble and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between SpeechBubble and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between SpeechBubble and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between SpeechBubble and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between SpeechBubble and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between SpeechBubble and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between SpeechBubble and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between SpeechBubble and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between SpeechBubble and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between SpeechBubble and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between SpeechBubble and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between SpeechBubble and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between SpeechBubble and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between SpeechBubble and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between SpeechBubble and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:0 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:NaN Protected Methods Fraction:NaN Package Access Methods Fraction:NaN Private  Methods Fraction:NaN Average Method Access:NaN Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Component bridge in Identifier mp.bridge.SpeechBubble is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Component bubble in Identifier mp.bridge.SpeechBubble is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Component speech in Identifier mp.bridge.SpeechBubble is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Super types: mp.bridge.AbstractStringShape [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:0: Component at in Identifier at is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Access modifiers used: Access Modifiers Used: [(mp.bridge.StringShape, public, public, 0, main.BridgeSceneImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:2 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:1 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Component bridge in Identifier mp.bridge.StringShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Component shape in Identifier mp.bridge.StringShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Component string in Identifier mp.bridge.StringShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getText:->String]Setters:[default setText:String->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Properties defined: Properties:[editable, g-s:0 p-v:5 access:package Text:String(default ,default )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Super types: [mp.shapes.Locatable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component change in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component current in Identifier current is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component event in Identifier event is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component height in Identifier height is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component height in Identifier oldHeight is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component listener in Identifier listener is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component listeners in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component old in Identifier old is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component old in Identifier oldHeight is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component old in Identifier oldWidth is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component old in Identifier oldX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component old in Identifier oldY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component percentage in Identifier percentage is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component property in Identifier property is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component property in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component width in Identifier oldWidth is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component width in Identifier width is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component x in Identifier oldX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component y in Identifier oldY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Class mp.shapes.AbstractAScaleRectangle with instance methods does not have exactly one interface.       [ClassHasOneInterface]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Consider separating the following method sets into different types [[public  setHeight:int->void, public  getHeight:->int, public  setY:int->void, public  getWidth:->int, public  setWidth:int->void, public  scale:int->void, public  setX:int->void, public  getX:->int, public  getY:->int], [public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  notify:String;Object;Object->void]] [ClassDecomposition]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Expected getter for property Height of type int in parent type mp.shapes.AbstractAScaleRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Expected getter for property Width of type int in parent type mp.shapes.AbstractAScaleRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Expected instantiation of java.beans.PropertyChangeEvent in type mp.shapes.AbstractAScaleRectangle[@Comp301Tags.BOUNDED_SHAPE] by methods [public  notify:String;Object;Object->void]. Good! [ExpectedClassInstantiations]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Expected setter for property Height of type int in parent type mp.shapes.AbstractAScaleRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Expected setter for property Width of type int in parent type mp.shapes.AbstractAScaleRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractAScaleRectangle and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractAScaleRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: Some method (setX:int->void) in class mp.shapes.AbstractAScaleRectangle:[@Comp301Tags.BOUNDED_SHAPE] has made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:1: The following public methods do not override: [public  notify:String;Object;Object->void] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:14: Access modifiers used: Access Modifiers Used: [(mp.shapes.AbstractAScaleRectangle, public, private, 3, mp.shapes.AbstractAScaleRectangle, null ), (mp.shapes.AbstractAScaleRectangle, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.AbstractAScaleRectangle, public, public, 0, main.Assignment2, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:14: Aggregate statistics:  Is Abstract:true Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:1 Number of Methods:11 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:8 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:5 Public Variables Fraction:0.0 Protected Variables Fraction:0.8 Package Access Variables Fraction:0.0 Private  Variable Fraction:0.2 Average Variable Access:1.4000000000000001 Number of Properties:4 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:2.0 Average Local References per Variable:9.2 Average Local Assignments per Variable:9.2 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:14: Component a in Identifier mp.shapes.AbstractAScaleRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:14: Component abstract in Identifier mp.shapes.AbstractAScaleRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:14: Component rectangle in Identifier mp.shapes.AbstractAScaleRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:14: Component scale in Identifier mp.shapes.AbstractAScaleRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:14: Component shapes in Identifier mp.shapes.AbstractAScaleRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:14: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:14: Interfaces defined: [mp.shapes.AScaleRectangleInterface, util.models.PropertyListenerRegisterer]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:14: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  scale:int->void, public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  notify:String;Object;Object->void]Getters:[public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int]Setters:[public  setX:int->void, public  setY:int->void, public  setWidth:int->void, public  setHeight:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:14: Missing supertype: @Comp301Tags.LOCATABLE of type mp.shapes.AbstractAScaleRectangle[@Comp301Tags.BOUNDED_SHAPE] [ExpectedSuperTypes]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:14: Properties defined: Properties:[editable, g-s:0 p-v:1 access:public X:int(public ,public ), editable, g-s:0 p-v:1 access:public Y:int(public ,public ), editable, g-s:0 p-v:1 access:public Height:int(public ,public ), editable, g-s:0 p-v:1 access:public Width:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:14: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:14: Type mp.shapes.AbstractAScaleRectangle matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:14: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:36: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:40: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:43: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:47: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:50: Parameter width should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:54: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:61: Parameter height should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:65: 'height' hides a field. [HiddenField]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:27: Signatures public  getX:->int common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:32: Signatures public  setX:int->void common with mp.shapes.AbstractPolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:32: Signatures public  setX:int->void common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:33:22: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:33:26: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:34:13: Variable 'oldX' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:39: Signatures public  getY:->int common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:44: Signatures public  setY:int->void common with mp.shapes.AbstractPolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:44: Signatures public  setY:int->void common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:45:22: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:45:26: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:46:13: Variable 'oldY' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:51: Signatures public  getWidth:->int common with mp.shapes.AbstractBoundedShape defined in common types [mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:51: Signatures public  getWidth:->int common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:56: Signatures public  setWidth:int->void common with mp.shapes.AbstractBoundedShape defined in common types [mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:56: Signatures public  setWidth:int->void common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:57:26: Parameter width should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:57:30: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:58:13: Variable 'oldWidth' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:60:16: The String "Width" appears 2 times in the file. [MultipleStringLiterals]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:63: Signatures public  getHeight:->int common with mp.shapes.AbstractBoundedShape defined in common types [mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:63: Signatures public  getHeight:->int common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:68: Signatures public  setHeight:int->void common with mp.shapes.AbstractBoundedShape defined in common types [mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:68: Signatures public  setHeight:int->void common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:69:27: Parameter height should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:69:31: 'height' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:70:13: Variable 'oldHeight' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:72:16: The String "Height" appears 2 times in the file. [MultipleStringLiterals]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:76:23: Parameter percentage should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:77:13: Variable 'oldWidth' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:78:13: Variable 'oldHeight' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:79: Magic number 100   [LiberalMagicNumber]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:80: Magic number 100   [LiberalMagicNumber]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:86:43: Parameter listener should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:90: Signatures public  notify:String;Object;Object->void common with mp.shapes.AbstractRotateLine not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:91:24: Parameter property should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:91:41: Parameter old should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:91:53: Parameter current should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:92:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:0: Component height in Identifier height is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:0: Component height in Identifier oldHeight is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:0: Component old in Identifier oldHeight is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:0: Component old in Identifier oldWidth is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:0: Component width in Identifier oldWidth is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:0: Component width in Identifier width is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: All public methods override. Good!  [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Expected getter for property Height of type int in parent type mp.shapes.AbstractBoundedShape[@Comp301Tags.LOCATABLE + @Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Expected getter for property Width of type int in parent type mp.shapes.AbstractBoundedShape[@Comp301Tags.LOCATABLE + @Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Expected getter for property X of type int in parent type mp.shapes.AbstractBoundedShape[@Comp301Tags.LOCATABLE + @Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Expected getter for property Y of type int in parent type mp.shapes.AbstractBoundedShape[@Comp301Tags.LOCATABLE + @Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Expected instantiation of java.beans.PropertyChangeEvent in type mp.shapes.AbstractBoundedShape[@Comp301Tags.LOCATABLE + @Comp301Tags.BOUNDED_SHAPE] by methods [public  notify:String;Object;Object->void]. Good! [ExpectedClassInstantiations]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Expected setter for property Height of type int in parent type mp.shapes.AbstractBoundedShape[@Comp301Tags.LOCATABLE + @Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Expected setter for property Width of type int in parent type mp.shapes.AbstractBoundedShape[@Comp301Tags.LOCATABLE + @Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Expected setter for property X of type int in parent type mp.shapes.AbstractBoundedShape[@Comp301Tags.LOCATABLE + @Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Expected setter for property Y of type int in parent type mp.shapes.AbstractBoundedShape[@Comp301Tags.LOCATABLE + @Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Missing getter for property PropertyChangeListeners of type List in parent type mp.shapes.AbstractBoundedShape[@Comp301Tags.LOCATABLE + @Comp301Tags.BOUNDED_SHAPE] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between AbstractBoundedShape and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between AbstractBoundedShape and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between AbstractBoundedShape and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between AbstractBoundedShape and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between AbstractBoundedShape and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between AbstractBoundedShape and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between AbstractBoundedShape and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between AbstractBoundedShape and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractBoundedShape and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractBoundedShape and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractBoundedShape and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractBoundedShape and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractBoundedShape and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractBoundedShape and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractBoundedShape and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractBoundedShape and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractBoundedShape and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractBoundedShape and mp.shapes.APolarPoint inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractBoundedShape and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractBoundedShape and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractBoundedShape and mp.shapes.AbstractPolarPoint inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractBoundedShape and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractBoundedShape and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractBoundedShape and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractBoundedShape and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractBoundedShape and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractBoundedShape and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractBoundedShape and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractBoundedShape and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractBoundedShape and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractBoundedShape and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractBoundedShape and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractBoundedShape and mp.shapes.APolarPoint inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractBoundedShape and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractBoundedShape and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractBoundedShape and mp.shapes.AbstractPolarPoint inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractBoundedShape and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractBoundedShape and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Some method (setWidth:int->void) in class mp.shapes.AbstractBoundedShape:[@Comp301Tags.BOUNDED_SHAPE] has made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). Good! [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:1: Some method (setWidth:int->void) in class mp.shapes.AbstractBoundedShape:[@Comp301Tags.LOCATABLE] has made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). Good! [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:8: Access modifiers used: Access Modifiers Used: [(mp.shapes.AbstractBoundedShape, public, private, 3, mp.shapes.AbstractBoundedShape, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:8: Aggregate statistics:  Is Abstract:true Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:4 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:2 Public Variables Fraction:0.0 Protected Variables Fraction:1.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:0.0 Average Variable Access:1.0 Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:9.0 Average Local Assignments per Variable:9.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:8: Component abstract in Identifier mp.shapes.AbstractBoundedShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:8: Component bounded in Identifier mp.shapes.AbstractBoundedShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:8: Component shape in Identifier mp.shapes.AbstractBoundedShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:8: Component shapes in Identifier mp.shapes.AbstractBoundedShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:8: Expected interface util.models.PropertyListenerRegisterer of class mp.shapes.AbstractBoundedShape[@Comp301Tags.LOCATABLE + @Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedInterfaces]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:8: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:8: Interfaces defined: [mp.shapes.BoundedShape]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:8: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getWidth:->int, public  getHeight:->int]Setters:[public  setWidth:int->void, public  setHeight:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:8: Missing supertype: @Comp301Tags.LOCATABLE of type mp.shapes.AbstractBoundedShape[@Comp301Tags.LOCATABLE + @Comp301Tags.BOUNDED_SHAPE] [ExpectedSuperTypes]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:8: Properties defined: Properties:[editable, g-s:0 p-v:1 access:public Height:int(public ,public ), editable, g-s:0 p-v:1 access:public Width:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:8: Super types: mp.shapes.AbstractLocatable [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:8: Type mp.shapes.AbstractBoundedShape matches tags (@Comp301Tags.LOCATABLE + @Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:8: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:13:33: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:13:40: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:13:47: Parameter width should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:13:51: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:13:58: Parameter height should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:13:62: 'height' hides a field. [HiddenField]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:19: Signatures public  getWidth:->int common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:24: Signatures public  setWidth:int->void common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:25:26: Parameter width should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:25:30: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:26:13: Variable 'oldWidth' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:31: Signatures public  getHeight:->int common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:36: Signatures public  setHeight:int->void common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:37:27: Parameter height should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:37:31: 'height' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:38:13: Variable 'oldHeight' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:0: Component change in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:0: Component current in Identifier current is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:0: Component event in Identifier event is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:0: Component listener in Identifier listener is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:0: Component listeners in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:0: Component old in Identifier old is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:0: Component old in Identifier oldX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:0: Component old in Identifier oldY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:0: Component property in Identifier property is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:0: Component property in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:0: Component x in Identifier oldX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:0: Component y in Identifier oldY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Class mp.shapes.AbstractLocatable with instance methods does not have exactly one interface.       [ClassHasOneInterface]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Consider separating the following method sets into different types [[public  getX:->int, public  setY:int->void, public  setX:int->void, public  getY:->int], [public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  notify:String;Object;Object->void]] [ClassDecomposition]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractLocatable and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractLocatable and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractLocatable and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractLocatable and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractLocatable and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractLocatable and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractLocatable and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractLocatable and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractLocatable and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractLocatable and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractLocatable and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractLocatable and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractLocatable and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractLocatable and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractLocatable and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractLocatable and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractLocatable and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractLocatable and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractLocatable and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractLocatable and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractLocatable and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractLocatable and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractLocatable and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractLocatable and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractLocatable and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractLocatable and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:1: The following public methods do not override: [public  notify:String;Object;Object->void] [PublicMethodsOverride]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:3:8: Unused import - util.annotations.Tags. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:12:8: Unused import - tags301.Comp301Tags. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:14: Access modifiers used: Access Modifiers Used: [(mp.shapes.AbstractLocatable, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.AbstractLocatable, public, package, 2, mp.shapes.AbstractBoundedShape, null ), (mp.shapes.AbstractLocatable, public, public, 0, main.Assignment2, null ), (mp.shapes.AbstractLocatable, public, private, 3, mp.shapes.AbstractLocatable, null ), (mp.shapes.AbstractLocatable, public, package, 2, mp.shapes.AbstractPolarPoint, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:14: Aggregate statistics:  Is Abstract:true Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:1 Number of Methods:6 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.6666666666666666 Package Access Variables Fraction:0.0 Private  Variable Fraction:0.3333333333333333 Average Variable Access:1.6666666666666665 Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:2.0 Average Local References per Variable:11.333333333333334 Average Local Assignments per Variable:11.333333333333334 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:14: Component abstract in Identifier mp.shapes.AbstractLocatable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:14: Component locatable in Identifier mp.shapes.AbstractLocatable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:14: Component shapes in Identifier mp.shapes.AbstractLocatable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:14: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:14: Interfaces defined: [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:14: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  notify:String;Object;Object->void]Getters:[public  getX:->int, public  getY:->int]Setters:[public  setX:int->void, public  setY:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:14: Missing supertype: @Comp301Tags.LOCATABLE of type mp.shapes.AbstractLocatable [ExpectedSuperTypes]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:14: Properties defined: Properties:[editable, g-s:0 p-v:1 access:public X:int(public ,public ), editable, g-s:0 p-v:1 access:public Y:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:14: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:14: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:14: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:24:30: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:24:34: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:24:37: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:24:41: 'y' hides a field. [HiddenField]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:29: Signatures public  getX:->int common with mp.bridge.AbstractImageShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:29: Signatures public  getX:->int common with mp.bridge.AbstractStringShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:29: Signatures public  getX:->int common with mp.shapes.ALocatable defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:29: Signatures public  getX:->int common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:29: Signatures public  getX:->int common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:34: Signatures public  setX:int->void common with mp.bridge.AbstractImageShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:34: Signatures public  setX:int->void common with mp.bridge.AbstractStringShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:34: Signatures public  setX:int->void common with mp.shapes.ALocatable defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:34: Signatures public  setX:int->void common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:34: Signatures public  setX:int->void common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:35:22: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:35:26: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:36:13: Variable 'oldX' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:41: Signatures public  getY:->int common with mp.bridge.AbstractImageShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:41: Signatures public  getY:->int common with mp.bridge.AbstractStringShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:41: Signatures public  getY:->int common with mp.shapes.ALocatable defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:41: Signatures public  getY:->int common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:41: Signatures public  getY:->int common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:46: Signatures public  setY:int->void common with mp.bridge.AbstractImageShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:46: Signatures public  setY:int->void common with mp.bridge.AbstractStringShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:46: Signatures public  setY:int->void common with mp.shapes.ALocatable defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:46: Signatures public  setY:int->void common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:46: Signatures public  setY:int->void common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:47:22: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:47:26: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:48:13: Variable 'oldY' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:53: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.AbstractImageShape defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:53: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.AbstractStringShape defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:53: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable, util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:53: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.RotatingLine defined in common types [util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:54:43: Parameter listener should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:58: Signatures public  notify:String;Object;Object->void common with mp.bridge.AbstractImageShape not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:58: Signatures public  notify:String;Object;Object->void common with mp.bridge.AbstractStringShape not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:58: Signatures public  notify:String;Object;Object->void common with mp.shapes.AbstractAScaleRectangle not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:58: Signatures public  notify:String;Object;Object->void common with mp.shapes.AbstractRotateLine not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:58: Signatures public  notify:String;Object;Object->void common with mp.shapes.RotatingLine not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:59:24: Parameter property should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:59:41: Parameter old should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:59:53: Parameter current should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:60:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:0: Component angle in Identifier angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:0: Component angle in Identifier oldAngle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:0: Component old in Identifier oldAngle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:0: Component old in Identifier oldRadius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:0: Component old in Identifier oldX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:0: Component old in Identifier oldY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:0: Component radius in Identifier oldRadius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:0: Component radius in Identifier radius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:0: Component x in Identifier oldX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:0: Component y in Identifier oldY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:0: Method updateCartesian:->void has access, private, and needs access, [private], (in used classes, [mp.shapes.AbstractPolarPoint]}. Good! [MethodAccessModifier]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: All public methods override. Good!  [PublicMethodsOverride]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:1 access:public Angle:double(public ,public ) common between AbstractPolarPoint and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:1 access:public Angle:double(public ,public ) common between AbstractPolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:1 access:public Radius:double(public ,public ) common between AbstractPolarPoint and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:1 access:public Radius:double(public ,public ) common between AbstractPolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between AbstractPolarPoint and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between AbstractPolarPoint and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between AbstractPolarPoint and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between AbstractPolarPoint and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between AbstractPolarPoint and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between AbstractPolarPoint and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between AbstractPolarPoint and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between AbstractPolarPoint and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between AbstractPolarPoint and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between AbstractPolarPoint and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between AbstractPolarPoint and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between AbstractPolarPoint and mp.shapes.AbstractBoundedShape inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between AbstractPolarPoint and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between AbstractPolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between AbstractPolarPoint and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between AbstractPolarPoint and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between AbstractPolarPoint and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between AbstractPolarPoint and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between AbstractPolarPoint and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between AbstractPolarPoint and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between AbstractPolarPoint and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between AbstractPolarPoint and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between AbstractPolarPoint and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between AbstractPolarPoint and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between AbstractPolarPoint and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between AbstractPolarPoint and mp.shapes.AbstractBoundedShape inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between AbstractPolarPoint and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between AbstractPolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:3:8: Unused import - util.annotations.Tags. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:6:8: Unused import - tags301.Comp301Tags. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:8: Access modifiers used: Access Modifiers Used: [(mp.shapes.AbstractPolarPoint, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.AbstractPolarPoint, public, public, 0, main.Assignment2, null ), (mp.shapes.AbstractPolarPoint, public, private, 3, mp.shapes.AbstractPolarPoint, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:8: Aggregate statistics:  Is Abstract:true Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:8 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:2 Public Methods Fraction:0.75 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.25 Average Method Access:0.75 Number of Variables:2 Public Variables Fraction:0.0 Protected Variables Fraction:1.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:0.0 Average Variable Access:1.0 Number of Properties:4 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:14.0 Average Local Assignments per Variable:14.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:8: Component abstract in Identifier mp.shapes.AbstractPolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:8: Component point in Identifier mp.shapes.AbstractPolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:8: Component polar in Identifier mp.shapes.AbstractPolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:8: Component shapes in Identifier mp.shapes.AbstractPolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:8: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:8: Interfaces defined: [mp.shapes.PolarPointInterface]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:8: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[private  updateCartesian:->void, private  updatePolar:->void]Getters:[public  getRadius:->double, public  getAngle:->double]Setters:[public  setRadius:double->void, public  setAngle:double->void, public  setX:int->void, public  setY:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:8: Missing supertype: @Comp301Tags.LOCATABLE of type mp.shapes.AbstractPolarPoint [ExpectedSuperTypes]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:8: Properties defined: Properties:[editable, g-s:0 p-v:1 access:public Radius:double(public ,public ), editable, g-s:0 p-v:1 access:public Angle:double(public ,public ), editable, g-s:0 p-v:5 access:public X:int(public ,public ), editable, g-s:0 p-v:5 access:public Y:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:8: Super types: mp.shapes.AbstractLocatable [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:8: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:8: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:12:31: Parameter radius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:12:38: 'radius' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:12:46: Parameter angle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:12:53: 'angle' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:19:31: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:19:38: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:30:27: Parameter radius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:30:34: 'radius' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:31:16: Variable 'oldRadius' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:34:16: The String "Radius" appears 2 times in the file. [MultipleStringLiterals]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:43:26: Parameter angle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:43:33: 'angle' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:44:16: Variable 'oldAngle' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:47:16: The String "Angle" appears 2 times in the file. [MultipleStringLiterals]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:51:13: Variable 'oldX' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:52:13: Variable 'oldY' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:60:16: Variable 'oldRadius' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:61:16: Variable 'oldAngle' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:69:22: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:75:22: Parameter y should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component angle in Identifier angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component angle in Identifier oldAngle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component current in Identifier current is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component height in Identifier height is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component old in Identifier old is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component old in Identifier oldAngle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component old in Identifier oldRadius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component point in Identifier point is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component property in Identifier property is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component radius in Identifier oldRadius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component radius in Identifier radius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component unit in Identifier UNIT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component units in Identifier units is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component width in Identifier width is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Expected getter for property X of type int in parent type mp.shapes.AbstractRotateLine[@Comp301Tags.LOCATABLE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Expected getter for property Y of type int in parent type mp.shapes.AbstractRotateLine[@Comp301Tags.LOCATABLE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Expected setter for property X of type int in parent type mp.shapes.AbstractRotateLine[@Comp301Tags.LOCATABLE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Expected setter for property Y of type int in parent type mp.shapes.AbstractRotateLine[@Comp301Tags.LOCATABLE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Missing getter for property PropertyChangeListeners of type List in parent type mp.shapes.AbstractRotateLine[@Comp301Tags.LOCATABLE] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Missing instantiation of java.beans.PropertyChangeEvent in type mp.shapes.AbstractRotateLine[@Comp301Tags.LOCATABLE]. [ExpectedClassInstantiations]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: No method in class mp.shapes.AbstractRotateLine:[@Comp301Tags.LOCATABLE] has not made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Angle:double(public ,public ) common between AbstractRotateLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Angle:double(public ,public ) common between AbstractRotateLine and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between AbstractRotateLine and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between AbstractRotateLine and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between AbstractRotateLine and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Radius:double(public ,public ) common between AbstractRotateLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Radius:double(public ,public ) common between AbstractRotateLine and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between AbstractRotateLine and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between AbstractRotateLine and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between AbstractRotateLine and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractRotateLine and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractRotateLine and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractRotateLine and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractRotateLine and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractRotateLine and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractRotateLine and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractRotateLine and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractRotateLine and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractRotateLine and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractRotateLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractRotateLine and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractRotateLine and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractRotateLine and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractRotateLine and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AbstractRotateLine and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractRotateLine and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractRotateLine and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractRotateLine and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractRotateLine and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractRotateLine and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractRotateLine and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractRotateLine and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractRotateLine and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractRotateLine and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractRotateLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractRotateLine and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractRotateLine and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractRotateLine and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractRotateLine and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AbstractRotateLine and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:1: The following public methods do not override: [public  notify:String;Object;Object->void] [PublicMethodsOverride]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:7:8: Unused import - util.models.PropertyListenerRegisterer. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:10: Access modifiers used: Access Modifiers Used: [(mp.shapes.AbstractRotateLine, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.AbstractRotateLine, public, public, 0, main.Assignment2, null ), (mp.shapes.AbstractRotateLine, public, private, 3, mp.shapes.AbstractRotateLine, null ), (mp.shapes.AbstractRotateLine, public, public, 0, main.BridgeSceneImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:10: Aggregate statistics:  Is Abstract:true Number of Abstract Methods:1 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:15 Number of Functions:6 Number of Non Getter Functions:0 Number of Getters and Setters:12 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:4 Public Variables Fraction:0.0 Protected Variables Fraction:1.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:0.0 Average Variable Access:1.0 Number of Properties:6 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:1.0 Average Local References per Variable:7.75 Average Local Assignments per Variable:7.75 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:10: Component abstract in Identifier mp.shapes.AbstractRotateLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:10: Component line in Identifier mp.shapes.AbstractRotateLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:10: Component rotate in Identifier mp.shapes.AbstractRotateLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:10: Component shapes in Identifier mp.shapes.AbstractRotateLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:10: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:10: Interfaces defined: [mp.shapes.RotateLine]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:10: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  rotate:int->void, public  move:int;int->void, public  notify:String;Object;Object->void]Getters:[public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int, public  getRadius:->double, public  getAngle:->double]Setters:[public  setX:int->void, public  setY:int->void, public  setWidth:int->void, public  setHeight:int->void, public  setRadius:double->void, public  setAngle:double->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:10: Missing interface util.models.PropertyListenerRegisterer of class mp.shapes.AbstractRotateLine[@Comp301Tags.LOCATABLE] [ExpectedInterfaces]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:10: Properties defined: Properties:[editable, g-s:0 p-v:1 access:public Radius:double(public ,public ), editable, g-s:0 p-v:1 access:public Angle:double(public ,public ), editable, g-s:0 p-v:1 access:public X:int(public ,public ), editable, g-s:0 p-v:1 access:public Y:int(public ,public ), editable, g-s:0 p-v:1 access:public Height:int(public ,public ), editable, g-s:0 p-v:1 access:public Width:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:10: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:10: Type mp.shapes.AbstractRotateLine matches tags (@Comp301Tags.LOCATABLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:10: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:13: Interface PolarPointInterface used as the type of variable/function point. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:15:35: Named Constant UNIT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:28: Signatures public  setX:int->void common with mp.shapes.AbstractPolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:29:22: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:29:26: 'x' hides a field. [HiddenField]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:39: Signatures public  setY:int->void common with mp.shapes.AbstractPolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:40:22: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:40:26: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:56:26: Parameter width should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:57:16: The String "Width" appears 4 times in the file. [MultipleStringLiterals]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:61:27: Parameter height should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:62:16: The String "Height" appears 4 times in the file. [MultipleStringLiterals]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:65: Signatures public  getRadius:->double common with mp.shapes.AbstractPolarPoint defined in common types [mp.shapes.PolarPointInterface, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:70: Signatures public  setRadius:double->void common with mp.shapes.AbstractPolarPoint defined in common types [mp.shapes.PolarPointInterface, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:71:27: Parameter radius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:72:16: Variable 'oldRadius' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:79: Signatures public  getAngle:->double common with mp.shapes.AbstractPolarPoint defined in common types [mp.shapes.PolarPointInterface, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:84: Signatures public  setAngle:double->void common with mp.shapes.AbstractPolarPoint defined in common types [mp.shapes.PolarPointInterface, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:85:26: Parameter angle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:86:16: Variable 'oldAngle' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:94:24: Parameter units should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:98: Signatures public  move:int;int->void common with mp.bridge.AvatarImpl defined in common types [mp.shapes.Moveable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:99:22: Parameter deltaX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:99:34: Parameter deltaY should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:100:13: Variable 'height' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:101:13: Variable 'width' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: All public methods override. Good!  [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Expected getter for property X of type int in parent type mp.shapes.ALocatable[@Comp301Tags.LOCATABLE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Expected getter for property Y of type int in parent type mp.shapes.ALocatable[@Comp301Tags.LOCATABLE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Expected setter for property X of type int in parent type mp.shapes.ALocatable[@Comp301Tags.LOCATABLE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Expected setter for property Y of type int in parent type mp.shapes.ALocatable[@Comp301Tags.LOCATABLE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Missing getter for property PropertyChangeListeners of type List in parent type mp.shapes.ALocatable[@Comp301Tags.LOCATABLE] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Missing instantiation of java.beans.PropertyChangeEvent in type mp.shapes.ALocatable[@Comp301Tags.LOCATABLE]. [ExpectedClassInstantiations]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: No method in class mp.shapes.ALocatable:[@Comp301Tags.LOCATABLE] has not made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between ALocatable and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between ALocatable and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:10: Access modifiers used: Access Modifiers Used: [(mp.shapes.ALocatable, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.ALocatable, public, private, 3, mp.shapes.ALocatable, null ), (mp.shapes.ALocatable, public, public, 0, main.Assignment2, null ), (mp.shapes.ALocatable, public, public, 0, main.BridgeSceneImpl, null ), (mp.shapes.ALocatable, public, package, 2, mp.shapes.AbstractRotateLine, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:10: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:4 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:2 Public Variables Fraction:0.0 Protected Variables Fraction:1.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:0.0 Average Variable Access:1.0 Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:8.0 Average Local Assignments per Variable:8.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:10: Component a in Identifier mp.shapes.ALocatable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:10: Component locatable in Identifier mp.shapes.ALocatable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:10: Component shapes in Identifier mp.shapes.ALocatable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:10: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:10: Interfaces defined: [mp.shapes.Locatable]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:10: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getX:->int, public  getY:->int]Setters:[public  setX:int->void, public  setY:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:10: Missing interface util.models.PropertyListenerRegisterer of class mp.shapes.ALocatable[@Comp301Tags.LOCATABLE] [ExpectedInterfaces]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:10: Properties defined: Properties:[editable, g-s:0 p-v:1 access:public X:int(public ,public ), editable, g-s:0 p-v:1 access:public Y:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:10: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:10: Type mp.shapes.ALocatable matches tags (@Comp301Tags.LOCATABLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:10: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:22:23: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:22:27: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:22:30: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:22:34: 'y' hides a field. [HiddenField]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:27: Signatures public  getX:->int common with mp.bridge.AbstractImageShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:27: Signatures public  getX:->int common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:27: Signatures public  getX:->int common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:32: Signatures public  setX:int->void common with mp.bridge.AbstractImageShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:32: Signatures public  setX:int->void common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:32: Signatures public  setX:int->void common with mp.shapes.AbstractPolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:32: Signatures public  setX:int->void common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:33:22: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:33:26: 'x' hides a field. [HiddenField]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:37: Signatures public  getY:->int common with mp.bridge.AbstractImageShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:37: Signatures public  getY:->int common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:37: Signatures public  getY:->int common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:42: Signatures public  setY:int->void common with mp.bridge.AbstractImageShape defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:42: Signatures public  setY:int->void common with mp.shapes.AbstractAScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:42: Signatures public  setY:int->void common with mp.shapes.AbstractPolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:42: Signatures public  setY:int->void common with mp.shapes.AbstractRotateLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:43:22: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:43:26: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:1 access:public Angle:double(public ,public ) common between APolarPoint and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:1 access:public Angle:double(public ,public ) common between APolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:1 access:public Radius:double(public ,public ) common between APolarPoint and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:1 access:public Radius:double(public ,public ) common between APolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.shapes.AbstractBoundedShape inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.shapes.AbstractBoundedShape inherited. Good! [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:0 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:NaN Protected Methods Fraction:NaN Package Access Methods Fraction:NaN Private  Methods Fraction:NaN Average Method Access:NaN Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Component a in Identifier mp.shapes.APolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Component point in Identifier mp.shapes.APolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Component polar in Identifier mp.shapes.APolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Component shapes in Identifier mp.shapes.APolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Super types: mp.shapes.AbstractPolarPoint [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:4:28: Parameter theRadius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:4:46: Parameter theAngle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:7:28: Parameter theX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:7:38: Parameter theY should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Expected getter for property Height of type int in parent type mp.shapes.AScaleRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Expected getter for property Width of type int in parent type mp.shapes.AScaleRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Expected instantiation of java.beans.PropertyChangeEvent in type mp.shapes.AScaleRectangle[@Comp301Tags.BOUNDED_SHAPE] by methods [public  notify:String;Object;Object->void]. Good! [ExpectedClassInstantiations]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Expected setter for property Height of type int in parent type mp.shapes.AScaleRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Expected setter for property Width of type int in parent type mp.shapes.AScaleRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between AScaleRectangle and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between AScaleRectangle and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between AScaleRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between AScaleRectangle and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between AScaleRectangle and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between AScaleRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AScaleRectangle and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AScaleRectangle and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AScaleRectangle and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AScaleRectangle and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AScaleRectangle and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AScaleRectangle and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AScaleRectangle and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AScaleRectangle and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AScaleRectangle and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AScaleRectangle and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AScaleRectangle and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AScaleRectangle and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AScaleRectangle and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AScaleRectangle and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between AScaleRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AScaleRectangle and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AScaleRectangle and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AScaleRectangle and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AScaleRectangle and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AScaleRectangle and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AScaleRectangle and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AScaleRectangle and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AScaleRectangle and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AScaleRectangle and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AScaleRectangle and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AScaleRectangle and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AScaleRectangle and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AScaleRectangle and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AScaleRectangle and mp.shapes.AbstractRotateLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between AScaleRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Some method (setX:int->void) in class mp.shapes.AScaleRectangle:[@Comp301Tags.BOUNDED_SHAPE] has made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). Good! [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:8: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:8: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:0 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:NaN Protected Methods Fraction:NaN Package Access Methods Fraction:NaN Private  Methods Fraction:NaN Average Method Access:NaN Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:8: Component a in Identifier mp.shapes.AScaleRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:8: Component rectangle in Identifier mp.shapes.AScaleRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:8: Component scale in Identifier mp.shapes.AScaleRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:8: Component shapes in Identifier mp.shapes.AScaleRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:8: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:8: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:8: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:8: Missing supertype: @Comp301Tags.LOCATABLE of type mp.shapes.AScaleRectangle[@Comp301Tags.BOUNDED_SHAPE] [ExpectedSuperTypes]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:8: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:8: Super types: mp.shapes.AbstractAScaleRectangle [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:8: Type mp.shapes.AScaleRectangle matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:8: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:13:32: Parameter a should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:13:39: Parameter b should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:13:46: Parameter w should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:13:53: Parameter h should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:0: Component percentage in Identifier percentage is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:3 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:1.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Component a in Identifier mp.shapes.AScaleRectangleInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Component interface in Identifier mp.shapes.AScaleRectangleInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Component rectangle in Identifier mp.shapes.AScaleRectangleInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Component scale in Identifier mp.shapes.AScaleRectangleInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Component shapes in Identifier mp.shapes.AScaleRectangleInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  scale:int->void]Getters:[]Setters:[public  setHeight:int->void, public  setWidth:int->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Properties defined: Properties:[writeonly  p-v:5 access:public Height:int( null,public ), writeonly  p-v:5 access:public Width:int( null,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Super types: [mp.shapes.BoundedShape] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Type mp.shapes.AScaleRectangleInterface matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:4 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:4 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Component bounded in Identifier mp.shapes.BoundedShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Component shape in Identifier mp.shapes.BoundedShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Component shapes in Identifier mp.shapes.BoundedShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getWidth:->int, default getHeight:->int]Setters:[default setWidth:int->void, default setHeight:int->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Properties defined: Properties:[editable, g-s:0 p-v:5 access:package Height:int(default ,default ), editable, g-s:0 p-v:5 access:package Width:int(default ,default )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Super types: [mp.shapes.Locatable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Type mp.shapes.BoundedShape matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:0: Component avatar in Identifier avatar is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:0: Component bridge in Identifier bridgeScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:0: Component change in Identifier propertyChangeEvents is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:0: Component composite in Identifier compositeShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:0: Component console in Identifier consoleScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:0: Component events in Identifier propertyChangeEvents is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:0: Component evt in Identifier evt is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:0: Component instance in Identifier instance is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:0: Component property in Identifier propertyChangeEvents is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:0: Component scene in Identifier bridgeScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:0: Component scene in Identifier consoleScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:0: Component shape in Identifier compositeShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:0: Component shape in Identifier shape is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:1: No method in class mp.shapes.ConsoleScene:[@Comp301Tags.CONSOLE_SCENE_VIEW] has not made expected call @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE. [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:1: Some method (propertyChange:java.beans.PropertyChangeEvent->void) in class mp.shapes.ConsoleScene:[@Comp301Tags.CONSOLE_SCENE_VIEW] has made expected call java.io.PrintStream!println:*->.*. Good! [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:1: Some method (registerWithBridgeScene:mp.bridge.BridgeScene;mp.shapes.ConsoleSceneInterface->void) in class mp.shapes.ConsoleScene:[@Comp301Tags.CONSOLE_SCENE_VIEW] has made expected call (.*)!addPropertyChangeListener:PropertyChangeListener->void. Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:1: The following public methods do not override: [public  getPropertyChangeEvents:->java.util.List, public  clearPropertyChangeEvents:->void] [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:14: Access modifiers used: Access Modifiers Used: [(mp.shapes.ConsoleScene, public, public, 0, main.StaticFactoryClass, null ), (mp.shapes.ConsoleScene, public, public, 0, main.Assignment2, null ), (mp.shapes.ConsoleScene, public, private, 3, mp.shapes.ConsoleScene, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:14: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:7 Number of Functions:2 Number of Non Getter Functions:1 Number of Getters and Setters:1 Number of Non Public Methods:2 Public Methods Fraction:0.7142857142857143 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.2857142857142857 Average Method Access:0.8571428571428571 Number of Variables:2 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:1 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:3.0 Average Local Assignments per Variable:3.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:14: Component console in Identifier mp.shapes.ConsoleScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:14: Component scene in Identifier mp.shapes.ConsoleScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:14: Component shapes in Identifier mp.shapes.ConsoleScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:14: Expected interface java.beans.PropertyChangeListener of class mp.shapes.ConsoleScene[@Comp301Tags.CONSOLE_SCENE_VIEW]. Good! [ExpectedInterfaces]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:14: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:14: Interfaces defined: [mp.shapes.ConsoleSceneInterface]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:14: Methods defined: NonGetterFunctions:[static public  consoleSceneViewFactoryMethod:->mp.shapes.ConsoleSceneInterface]NonSetterProcedures:[public  propertyChange:java.beans.PropertyChangeEvent->void, public  clearPropertyChangeEvents:->void, static public  registerWithBridgeScene:mp.bridge.BridgeScene;mp.shapes.ConsoleSceneInterface->void, static private  registerWithAvatar:mp.bridge.Avatar;mp.shapes.ConsoleSceneInterface->void, static private  registerWithCompositeShape:Object;mp.shapes.ConsoleSceneInterface->void]Getters:[public  getPropertyChangeEvents:->java.util.List]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:14: Properties defined: Properties:[readonly  p-v:3 access:public PropertyChangeEvents:java.util.List(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:14: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:14: Type mp.shapes.ConsoleScene matches tags (@Comp301Tags.CONSOLE_SCENE_VIEW)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:14: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:17: Interface ConsoleSceneInterface used as the type of variable/function instance. Good! [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:21:36: Parameter evt should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:40: Interface ConsoleSceneInterface used as the type of variable/function consoleSceneViewFactoryMethod. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:47: Interface BridgeScene used as the type of variable/function bridgeScene. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:47: Interface ConsoleSceneInterface used as the type of variable/function consoleScene. Good! [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:47:52: Parameter bridgeScene should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:47:77: Parameter consoleScene should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:64: Interface Avatar used as the type of variable/function avatar. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:64: Interface ConsoleSceneInterface used as the type of variable/function consoleScene. Good! [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:64:44: Parameter avatar should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:64:59: Parameter consoleScene should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:80: Interface ConsoleSceneInterface used as the type of variable/function consoleScene. Good! [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:80:52: Parameter shape should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:80:66: Parameter consoleScene should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:82: In method registerWithCompositeShape, rewrite if to use else-ifs  rather than nested ifs (that is, use else branching instead of then branching) [ThenBranching]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:83: Class mp.bridge.Shape rather than interface used as the type of variable/function compositeShape. [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:83:29: Variable 'compositeShape' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:0 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:NaN Protected Methods Fraction:NaN Package Access Methods Fraction:NaN Private  Methods Fraction:NaN Average Method Access:NaN Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Component console in Identifier mp.shapes.ConsoleSceneInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Component interface in Identifier mp.shapes.ConsoleSceneInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Component scene in Identifier mp.shapes.ConsoleSceneInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Component shapes in Identifier mp.shapes.ConsoleSceneInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Super types: [java.beans.PropertyChangeListener] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Type mp.shapes.ConsoleSceneInterface matches tags (@Comp301Tags.CONSOLE_SCENE_VIEW)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Access modifiers used: Access Modifiers Used: [(mp.shapes.Get, public, public, 0, main.BridgeSceneImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Component get in Identifier mp.shapes.Get is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Component shapes in Identifier mp.shapes.Get is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getLeftLine:->mp.shapes.RotateLine, public  getRightLine:->mp.shapes.RotateLine]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Properties defined: Properties:[readonly  p-v:5 access:public RightLine:mp.shapes.RotateLine(public , null), readonly  p-v:5 access:public LeftLine:mp.shapes.RotateLine(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Super types: [] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:4: Interface RotateLine used as the type of variable/function getLeftLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:5: Interface RotateLine used as the type of variable/function getRightLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Access modifiers used: Access Modifiers Used: [(mp.shapes.GetRectangle, public, public, 0, main.BridgeSceneImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:1 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:1 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:1 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Component get in Identifier mp.shapes.GetRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Component rectangle in Identifier mp.shapes.GetRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Component shapes in Identifier mp.shapes.GetRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getRectangle:->mp.shapes.AScaleRectangleInterface]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Properties defined: Properties:[readonly  p-v:5 access:public Rectangle:mp.shapes.AScaleRectangleInterface(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Super types: [mp.shapes.Get] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:4: Interface AScaleRectangleInterface used as the type of variable/function getRectangle. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component height in Identifier height is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component left in Identifier leftLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component line in Identifier leftLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component line in Identifier rightLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component lower in Identifier lower is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component num in Identifier num1 is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component num in Identifier num2 is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component rectangle in Identifier rectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component right in Identifier rightLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component start in Identifier start is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component top in Identifier top is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component upper in Identifier upper is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:1: All public methods override. Good!  [PublicMethodsOverride]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:1: Property readonly  p-v:2 access:public LeftLine:mp.shapes.RotateLine(public , null) common between Gorge and mp.bridge.Shape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:1: Property readonly  p-v:2 access:public RightLine:mp.shapes.RotateLine(public , null) common between Gorge and mp.bridge.Shape not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Access modifiers used: Access Modifiers Used: [(mp.shapes.Gorge, public, private, 3, mp.shapes.Gorge, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:3 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:3 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:10 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:1.0 Private  Variable Fraction:0.0 Average Variable Access:2.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.8 Average Local Assignments per Variable:2.8 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Component gorge in Identifier mp.shapes.Gorge is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Component shapes in Identifier mp.shapes.Gorge is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Interfaces defined: [mp.shapes.GetRectangle]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getLeftLine:->mp.shapes.RotateLine, public  getRightLine:->mp.shapes.RotateLine, public  getRectangle:->mp.shapes.AScaleRectangleInterface]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Properties defined: Properties:[readonly  p-v:2 access:public RightLine:mp.shapes.RotateLine(public , null), readonly  p-v:2 access:public LeftLine:mp.shapes.RotateLine(public , null), readonly  p-v:2 access:public Rectangle:mp.shapes.AScaleRectangleInterface(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:11: Interface RotateLine used as the type of variable/function leftLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:12: Interface RotateLine used as the type of variable/function rightLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:13: Interface AScaleRectangleInterface used as the type of variable/function rectangle. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:14:18: Final parameter x defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:32: Interface RotateLine used as the type of variable/function getLeftLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:36: Interface RotateLine used as the type of variable/function getRightLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:40: Interface AScaleRectangleInterface used as the type of variable/function getRectangle. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Access modifiers used: Access Modifiers Used: [(mp.shapes.Locatable, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.Locatable, public, public, 0, main.Assignment2, null ), (mp.shapes.Locatable, public, package, 2, mp.shapes.AbstractRotateLine, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:4 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:4 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Component locatable in Identifier mp.shapes.Locatable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Component shapes in Identifier mp.shapes.Locatable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getX:->int, default getY:->int]Setters:[default setX:int->void, default setY:int->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Properties defined: Properties:[editable, g-s:0 p-v:5 access:package X:int(default ,default ), editable, g-s:0 p-v:5 access:package Y:int(default ,default )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Super types: [] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Type mp.shapes.Locatable matches tags (@Comp301Tags.LOCATABLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Access modifiers used: Access Modifiers Used: [(mp.shapes.Moveable, public, protected, 1, mp.bridge.AvatarImpl, null ), (mp.shapes.Moveable, public, public, 0, main.Assignment2, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:1 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Component moveable in Identifier mp.shapes.Moveable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Component shapes in Identifier mp.shapes.Moveable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  move:int;int->void]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Super types: [] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:0: Component angle in Identifier angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:0: Component radius in Identifier radius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Access modifiers used: Access Modifiers Used: [(mp.shapes.PolarPointInterface, public, package, 2, mp.shapes.AbstractRotateLine, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:4 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Component interface in Identifier mp.shapes.PolarPointInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Component point in Identifier mp.shapes.PolarPointInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Component polar in Identifier mp.shapes.PolarPointInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Component shapes in Identifier mp.shapes.PolarPointInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getAngle:->double, public  getRadius:->double]Setters:[public  setAngle:double->void, public  setRadius:double->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Properties defined: Properties:[editable, g-s:0 p-v:5 access:public Radius:double(public ,public ), editable, g-s:0 p-v:5 access:public Angle:double(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Super types: [mp.shapes.Locatable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:0: Component angle in Identifier angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:0: Component current in Identifier current is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:0: Component old in Identifier old is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:0: Component property in Identifier property is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:0: Component radius in Identifier radius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:0: Component units in Identifier units is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Access modifiers used: Access Modifiers Used: [(mp.shapes.RotateLine, public, public, 0, main.Assignment2, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:5 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:3 Number of Non Public Methods:4 Public Methods Fraction:0.2 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.8 Private  Methods Fraction:0.0 Average Method Access:1.6 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:3 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:0.3333333333333333 WriteOnly  Properties Fraction:0.6666666666666666 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Component line in Identifier mp.shapes.RotateLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Component rotate in Identifier mp.shapes.RotateLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Component shapes in Identifier mp.shapes.RotateLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[default rotate:int->void, public  notify:String;Object;Object->void]Getters:[default getHeight:->int]Setters:[default setRadius:double->void, default setAngle:double->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Properties defined: Properties:[writeonly  p-v:5 access:package Radius:double( null,default ), writeonly  p-v:5 access:package Angle:double( null,default ), readonly  p-v:5 access:package Height:int(default , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Super types: [mp.shapes.BoundedShape, mp.shapes.Moveable, mp.shapes.PolarPointInterface] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Type mp.shapes.RotateLine matches tags (@Comp301Tags.LOCATABLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component arg in Identifier arg0 is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component change in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component current in Identifier current is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component event in Identifier event is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component listeners in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component old in Identifier old is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component property in Identifier property is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component property in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component some in Identifier some is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: All public methods override. Good!  [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected getter for property Angle of type double in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected getter for property Radius of type double in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected getter for property X of type int in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected getter for property Y of type int in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected setter for property Angle of type double in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected setter for property Radius of type double in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected setter for property X of type int in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected setter for property Y of type int in parent type mp.shapes.RotatingLine[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Angle:double(public ,public ) common between RotatingLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Angle:double(public ,public ) common between RotatingLine and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between RotatingLine and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between RotatingLine and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Height:int(public ,public ) common between RotatingLine and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Radius:double(public ,public ) common between RotatingLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Radius:double(public ,public ) common between RotatingLine and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between RotatingLine and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between RotatingLine and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Width:int(public ,public ) common between RotatingLine and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RotatingLine and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RotatingLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RotatingLine and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RotatingLine and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RotatingLine and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RotatingLine and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public X:int(public ,public ) common between RotatingLine and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.AbstractImageShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.AbstractStringShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RotatingLine and mp.shapes.ALocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RotatingLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RotatingLine and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RotatingLine and mp.shapes.AbstractAScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RotatingLine and mp.shapes.AbstractBoundedShape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RotatingLine and mp.shapes.AbstractLocatable not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:1 access:public Y:int(public ,public ) common between RotatingLine and mp.shapes.AbstractPolarPoint not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:14: Access modifiers used: Access Modifiers Used: [(mp.shapes.RotatingLine, public, private, 3, mp.shapes.RotatingLine, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:14: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:1 Number of Methods:2 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:1 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:2.0 Average Local References per Variable:2.0 Average Local Assignments per Variable:2.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:14: Component line in Identifier mp.shapes.RotatingLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:14: Component rotating in Identifier mp.shapes.RotatingLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:14: Component shapes in Identifier mp.shapes.RotatingLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:14: Expected signature move:int;int->void in type mp.shapes.RotatingLine:[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:14: Expected signature rotate:int->void in type mp.shapes.RotatingLine:[@Comp301Tags.ROTATING_LINE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:14: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:14: Interfaces defined: [util.models.PropertyListenerRegisterer]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:14: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  notify:String;Object;Object->void]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:14: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:14: Super types: mp.shapes.AbstractRotateLine [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:14: Type mp.shapes.RotatingLine matches tags (@Comp301Tags.ROTATING_LINE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:14: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:22: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.bridge.AbstractImageShape defined in common types [util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:22: Signatures public  addPropertyChangeListener:java.beans.PropertyChangeListener->void common with mp.shapes.AbstractAScaleRectangle defined in common types [util.models.PropertyListenerRegisterer]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:23:47: Parameter arg0 should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:27: Signatures public  notify:String;Object;Object->void common with mp.bridge.AbstractImageShape not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:27: Signatures public  notify:String;Object;Object->void common with mp.shapes.AbstractAScaleRectangle not defined in a common type  [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:28:24: Parameter property should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:28:41: Parameter old should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:28:53: Parameter current should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:29:29: Variable 'event' should be declared final. [FinalLocalVariable]
Audit done.
