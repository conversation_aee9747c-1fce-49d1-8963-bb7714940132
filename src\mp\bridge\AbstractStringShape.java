package mp.bridge;

import util.annotations.Visible;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import util.models.PropertyListenerRegisterer;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.List;
import java.util.ArrayList;

@StructurePattern(StructurePatternNames.STRING_PATTERN)
public abstract class AbstractStringShape implements StringShape, PropertyListenerRegisterer {
    protected String text;
    protected int x, y;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();
    
    public AbstractStringShape(String defaultText) {
        this.text = defaultText;
        this.x = 0;
        this.y = 0;
    }
    
    @Override
    public String getText() {
        return text;
    }
    
    @Override
    public void setText(String text) {
        String oldText = this.text;
        this.text = text;
        notify("Text", oldText, text);
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(int x) {
        int oldX = this.x;
        this.x = x;
        notify("X", oldX, x);
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(int y) {
        int oldY = this.y;
        this.y = y;
        notify("Y", oldY, y);
    }

    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        propertyChangeListeners.add(listener);
    }

    @Visible(false)
    public void notify(String property, Object old, Object current) {
        PropertyChangeEvent event = new PropertyChangeEvent(this, property, old, current);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }
}
