<Events startTimestamp="1752112638210" logVersion="1.0.0.202503121800">
  <Command __id="2" _type="ShellCommand" date="Wed Jul 09 21:57:34 EDT 2025" starttimestamp="1752112638210" timestamp="15994" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="5" _type="EclipseCommand" commandID="" date="Wed Jul 09 21:58:17 EDT 2025" starttimestamp="1752112638210" timestamp="59498" />
  <Command __id="7" _type="MoveCaretCommand" caretOffset="295" date="Wed Jul 09 21:58:27 EDT 2025" docOffset="295" starttimestamp="1752112638210" timestamp="68858" />
  <Command __id="8" _type="SelectTextCommand" caretOffset="302" date="Wed Jul 09 21:58:27 EDT 2025" end="302" start="283" starttimestamp="1752112638210" timestamp="68987" />
  <Command __id="9" _type="ShellCommand" date="Wed Jul 09 21:58:40 EDT 2025" starttimestamp="1752112638210" timestamp="82476" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="10" _type="ShellCommand" date="Wed Jul 09 21:59:03 EDT 2025" starttimestamp="1752112638210" timestamp="104831" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="11" _type="ShellCommand" date="Wed Jul 09 21:59:13 EDT 2025" starttimestamp="1752112638210" timestamp="114802" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="12" _type="ShellCommand" date="Wed Jul 09 21:59:15 EDT 2025" starttimestamp="1752112638210" timestamp="117367" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="13" _type="EclipseCommand" commandID="" date="Wed Jul 09 21:59:15 EDT 2025" starttimestamp="1752112638210" timestamp="117377" />
  <Command __id="14" _type="EclipseCommand" commandID="org.eclipse.ui.project.cleanAction" date="Wed Jul 09 21:59:15 EDT 2025" starttimestamp="1752112638210" timestamp="117383" />
  <Command __id="15" _type="EclipseCommand" commandID="" date="Wed Jul 09 21:59:15 EDT 2025" starttimestamp="1752112638210" timestamp="117577" />
  <Command __id="16" _type="EclipseCommand" commandID="" date="Wed Jul 09 21:59:16 EDT 2025" starttimestamp="1752112638210" timestamp="117917" />
  <Command __id="18" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 21:59:29 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752112638210" timestamp="130811" type="Run" />
  <Command __id="19" _type="RunCommand" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 21:59:29 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752112638210" timestamp="130813" type="Run" />
  <Command __id="20" _type="ShellCommand" date="Wed Jul 09 21:59:33 EDT 2025" starttimestamp="1752112638210" timestamp="134832" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="21" _type="ConsoleOutput" date="Wed Jul 09 21:59:47 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="148850" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Factory
<<
]]></outputString>
    <diff><![CDATA[null]]></diff>
  </Command>
  <Command __id="22" _type="ConsoleOutput" date="Wed Jul 09 21:59:47 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="148913" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 21:59:47 EDT 2025<<
>>Running test TaggedFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2"), Diff(INSERT,"Wed Jul 09 21:59:47 EDT 2025<<
¶>>Running test Tagged"), Diff(EQUAL,"Factory
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="23" _type="ConsoleOutput" date="Wed Jul 09 21:59:47 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="148924" type="ConsoleOutput">
    <outputString><![CDATA[>>TaggedFactory test execution time (ms):43<<
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 21:59:47 EDT 2025<<
¶>>Running test TaggedFactory"), Diff(INSERT,"TaggedFactory test execution time (ms):43<<
¶>>Test Result:
¶TaggedFactory,100.0% complete,2.0,2.0,"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="24" _type="ConsoleOutput" date="Wed Jul 09 21:59:47 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="148930" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 21:59:47 EDT 2025<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running checkstyle, this will take time<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"TaggedFactory test execution time (ms):43<<
¶>>Test Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶"), Diff(INSERT,"Wed Jul 09 21:59:47 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined
¶<<
¶>>Running checkstyle, this will take time"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="25" _type="ConsoleOutput" date="Wed Jul 09 21:59:47 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="148978" type="ConsoleOutput">
    <outputString><![CDATA[WARNING: A terminally deprecated method in java.lang.System has been called
WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
WARNING: System::setSecurityManager will be removed in a future release
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Wed Jul 09 21:59:47 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined
¶<<
¶>>Running checkstyle, this will take time<<"), Diff(INSERT,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="26" _type="ConsoleOutput" date="Wed Jul 09 21:59:51 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="152920" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneFactoryMethodDefined test execution time (ms):3993<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(INSERT,">>BridgeSceneFactoryMethodDefined test execution time (ms):3993<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="27" _type="ConsoleOutput" date="Wed Jul 09 21:59:51 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="152927" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneFactoryMethodDefined"), Diff(DELETE," test execution time (ms):3993"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="28" _type="ConsoleOutput" date="Wed Jul 09 21:59:51 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="152931" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 21:59:51 EDT 2025<<
>>Running test BridgeSceneSingletonFromFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 21:59:51 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"FactoryMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"SingletonFromFactory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="29" _type="ConsoleOutput" date="Wed Jul 09 21:59:51 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="152936" type="ConsoleOutput">
    <outputString><![CDATA[Execution exception caused by invocation exception caused by:
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Wed Jul 09 21:59:51 EDT 2025<<
¶>>Running test BridgeSceneSingletonFromFactory
¶<<"), Diff(INSERT,"Execution exception caused by invocation exception caused by:"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="30" _type="ExceptionCommand" date="Wed Jul 09 21:59:51 EDT 2025" starttimestamp="1752112638210" timestamp="152939" type="Exception">
    <exceptionString><![CDATA[java.lang.Error: Unresolved compilation problem: 
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)

	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
	at mp.bridge.Shape.move(Shape.java:37)
	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
java.lang.Error: Unresolved compilation problem: 
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)

	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
	at mp.bridge.Shape.move(Shape.java:37)
	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
java.lang.AssertionError: Factory method returns null object%0.0
	at org.junit.Assert.fail(Assert.java:88)
	at org.junit.Assert.assertTrue(Assert.java:41)
	at grader.basics.testcase.PassFailJUnitTestCase.assertTrue(PassFailJUnitTestCase.java:339)
	at gradingTools.shared.testcases.FactoryMethodTest.maybeUseConstructor(FactoryMethodTest.java:144)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:411)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:316)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
]]></exceptionString>
    <language><![CDATA[SML]]></language>
  </Command>
  <Command __id="31" _type="ConsoleOutput" date="Wed Jul 09 21:59:51 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="152947" type="ConsoleOutput">
    <outputString><![CDATA[java.lang.Error: Unresolved compilation problem: 
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)

	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
	at mp.bridge.Shape.move(Shape.java:37)
	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
java.lang.Error: Unresolved compilation problem: 
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)

	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
	at mp.bridge.Shape.move(Shape.java:37)
	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
java.lang.AssertionError: Factory method returns null object%0.0
	at org.junit.Assert.fail(Assert.java:88)
	at org.junit.Assert.assertTrue(Assert.java:41)
	at grader.basics.testcase.PassFailJUnitTestCase.assertTrue(PassFailJUnitTestCase.java:339)
	at gradingTools.shared.testcases.FactoryMethodTest.maybeUseConstructor(FactoryMethodTest.java:144)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:411)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:316)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"Execution exception caused by invocation exception caused by:"), Diff(INSERT,"java.lang.Error: Unresolved compilation problem: 
¶	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
¶
¶	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
¶	at mp.bridge.Shape.move(Shape.java:37)
¶	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
¶	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
¶	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
¶	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
¶	at java.base/java.lang.Thread.run(Thread.java:833)
¶java.lang.Error: Unresolved compilation problem: 
¶	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
¶
¶	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
¶	at mp.bridge.Shape.move(Shape.java:37)
¶	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
¶	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
¶	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
¶	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
¶	at java.base/java.lang.Thread.run(Thread.java:833)
¶java.lang.AssertionError: Factory method returns null object%0.0
¶	at org.junit.Assert.fail(Assert.java:88)
¶	at org.junit.Assert.assertTrue(Assert.java:41)
¶	at grader.basics.testcase.PassFailJUnitTestCase.assertTrue(PassFailJUnitTestCase.java:339)
¶	at gradingTools.shared.testcases.FactoryMethodTest.maybeUseConstructor(FactoryMethodTest.java:144)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:411)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:316)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
¶	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
¶	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="32" _type="ConsoleOutput" date="Wed Jul 09 21:59:51 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="153036" type="ConsoleOutput">
    <outputString><![CDATA[>>Steps traced since last test:
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
 at[mp.shapes.RotatingLine.move(RotatingLine.java:18), mp.bridge.Shape.move(Shape.java:37), mp.bridge.AvatarImpl.move(AvatarImpl.java:47), main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53), main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15), java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method), java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:568), grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22), java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:833)]

>>BridgeSceneSingletonFromFactory test execution time (ms):13<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory method returns null object
<<
>>Wed Jul 09 21:59:51 EDT 2025<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"java.lang.Error: Unresolved compilation problem: "), Diff(INSERT,">>Steps traced since last test:"), Diff(EQUAL,"
¶	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
¶"), Diff(DELETE,"
¶	"), Diff(INSERT," "), Diff(EQUAL,"at"), Diff(DELETE," "), Diff(INSERT,"["), Diff(EQUAL,"mp.shapes.RotatingLine.move(RotatingLine.java:18)"), Diff(DELETE,"
¶	at"), Diff(INSERT,","), Diff(EQUAL," mp.bridge.Shape.move(Shape.java:37)"), Diff(DELETE,"
¶	at"), Diff(INSERT,","), Diff(EQUAL," mp.bridge.AvatarImpl.move(AvatarImpl.java:47)"), Diff(DELETE,"
¶	at"), Diff(INSERT,","), Diff(EQUAL," main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)"), Diff(DELETE,"
¶	at"), Diff(INSERT,","), Diff(EQUAL," main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)"), Diff(DELETE,"
¶	at"), Diff(INSERT,","), Diff(EQUAL," java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)"), Diff(DELETE,"
¶	at"), Diff(INSERT,","), Diff(EQUAL," java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)"), Diff(DELETE,"
¶	at"), Diff(INSERT,","), Diff(EQUAL," java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)"), Diff(DELETE,"
¶	at"), Diff(INSERT,","), Diff(EQUAL," java.base/java.lang.reflect.Method.invoke(Method.java:568)"), Diff(DELETE,"
¶	at"), Diff(INSERT,","), Diff(EQUAL," grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)"), Diff(DELETE,"
¶	at"), Diff(INSERT,","), Diff(EQUAL," java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)"), Diff(DELETE,"
¶	at"), Diff(INSERT,","), Diff(EQUAL," java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)"), Diff(DELETE,"
¶	at"), Diff(INSERT,","), Diff(EQUAL," java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)"), Diff(DELETE,"
¶	at"), Diff(INSERT,","), Diff(EQUAL," java.base/java.lang.Thread.run(Thread.java:833)"), Diff(DELETE,"
¶java.lang.Error: Unresolved compilation problem: 
¶	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
¶
¶	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
¶	at mp.bridge.Shape.move(Shape.java:37)
¶	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
¶	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
¶	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
¶	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
¶	at java.base/java.lang.Thread.run(Thread.java:833)
¶java.lang.AssertionError: Factory method returns null object%0.0
¶	at org.junit.Assert.fail(Assert.java:88)
¶	at org.junit.Assert.assertTrue(Assert.java:41)
¶	at grader.basics.testcase.PassFailJUnitTestCase.assertTrue(PassFailJUnitTestCase.java:339)
¶	at gradingTools.shared.testcases.FactoryMethodTest.maybeUseConstructor(FactoryMethodTest.java:144)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:411)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:316)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
¶	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
¶	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)"), Diff(INSERT,"]
¶
¶>>BridgeSceneSingletonFromFactory test execution time (ms):13<<
¶>>Test Result:
¶BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory method returns null object
¶<<
¶>>Wed Jul 09 21:59:51 EDT 2025<<
¶>>Running test ConsoleSceneViewFactoryMethodDefined
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="33" _type="ConsoleOutput" date="Wed Jul 09 21:59:51 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="153114" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewFactoryMethodDefined test execution time (ms):111<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 21:59:51 EDT 2025<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>ConsoleSceneViewSingletonFromFactory test execution time (ms):4<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Steps traced since last test:
¶	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
¶ at[mp.shapes.RotatingLine.move(RotatingLine.java:18), mp.bridge.Shape.move(Shape.java:37), mp.bridge.AvatarImpl.move(AvatarImpl.java:47), main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53), main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15), java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method), java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:568), grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22), java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:833)]
¶
¶>>BridgeSceneSingletonFromFactory"), Diff(INSERT,"ConsoleSceneViewFactoryMethodDefined"), Diff(EQUAL," test execution time (ms):1"), Diff(DELETE,"3"), Diff(INSERT,"11"), Diff(EQUAL,"<<
¶>>Test Result:
¶"), Diff(DELETE,"BridgeSceneSingletonFromFactory,"), Diff(INSERT,"ConsoleSceneViewFactoryMethodDefined,10"), Diff(EQUAL,"0.0% complete,"), Diff(DELETE,"0"), Diff(INSERT,"2"), Diff(EQUAL,".0,2.0,"), Diff(DELETE,"Factory method returns null object"), Diff(EQUAL,"
¶<<
¶>>Wed Jul 09 21:59:51 EDT 2025<<
¶>>Running test ConsoleSceneView"), Diff(DELETE,"FactoryMethodDefined
¶"), Diff(INSERT,"SingletonFromFactory
¶<<
¶>>ConsoleSceneViewSingletonFromFactory test execution time (ms):4"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="34" _type="ConsoleOutput" date="Wed Jul 09 21:59:51 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="153145" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 21:59:51 EDT 2025<<
>>Running test LegsFactoryMethodDefined
<<
>>LegsFactoryMethodDefined test execution time (ms):48<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>ConsoleSceneViewFactoryMethodDefined test execution time (ms):111<<
¶"), Diff(EQUAL,">>Test Result:
¶ConsoleSceneView"), Diff(DELETE,"FactoryMethodDefined"), Diff(INSERT,"SingletonFromFactory"), Diff(EQUAL,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 21:59:51 EDT 2025<<
¶>>Running test "), Diff(DELETE,"ConsoleSceneViewSingletonFromFactory
¶<<
¶>>ConsoleSceneViewSingletonFromFactory"), Diff(INSERT,"LegsFactoryMethodDefined
¶<<
¶>>LegsFactoryMethodDefined"), Diff(EQUAL," test execution time (ms):4"), Diff(INSERT,"8"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="35" _type="ConsoleOutput" date="Wed Jul 09 21:59:51 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="153152" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 21:59:51 EDT 2025<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Test Result:
¶"), Diff(DELETE,"ConsoleSceneViewSingletonFromFactory"), Diff(INSERT,"LegsFactoryMethodDefined"), Diff(EQUAL,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 21:59:51 EDT 2025<<
¶>>Running test "), Diff(DELETE,"LegsFactoryMethodDefined
¶<<
¶>>LegsFactoryMethodDefined test execution time (ms):48"), Diff(INSERT,"A2MainCallsBridgeSceneFactoryMethod
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="36" _type="ConsoleOutput" date="Wed Jul 09 21:59:51 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="153179" type="ConsoleOutput">
    <outputString><![CDATA[>>A2MainCallsBridgeSceneFactoryMethod test execution time (ms):41<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 21:59:51 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):41"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="37" _type="ConsoleOutput" date="Wed Jul 09 21:59:51 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="153185" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE," test execution time (ms):41"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="38" _type="ConsoleOutput" date="Wed Jul 09 21:59:51 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="153204" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 21:59:51 EDT 2025<<
>>Running test AvatarCallsLegFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 21:59:51 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="39" _type="ConsoleOutput" date="Wed Jul 09 21:59:51 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="153225" type="ConsoleOutput">
    <outputString><![CDATA[>>AvatarCallsLegFactoryMethod test execution time (ms):39<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 21:59:51 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod
¶"), Diff(INSERT,"AvatarCallsLegFactoryMethod test execution time (ms):39"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="40" _type="ConsoleOutput" date="Wed Jul 09 21:59:51 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="153230" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"AvatarCallsLegFactoryMethod"), Diff(DELETE," test execution time (ms):39"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="41" _type="ConsoleOutput" date="Wed Jul 09 21:59:56 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="158704" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2ConsoleSceneView
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,"), Diff(INSERT,"Running suite A2ConsoleSceneView"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="42" _type="ConsoleOutput" date="Wed Jul 09 21:59:56 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="158714" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 21:59:56 EDT 2025<<
>>Running test TaggedConsoleSceneView
<<
>>TaggedConsoleSceneView test execution time (ms):2<<
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Wed Jul 09 21:59:56 EDT 2025<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Wed Jul 09 21:59:56 EDT 2025<<
>>Running test TaggedLocatable
<<
>>Steps traced since last test:

>>TaggedLocatable test execution time (ms):2<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2ConsoleSceneView
¶"), Diff(INSERT,"Wed Jul 09 21:59:56 EDT 2025<<
¶>>Running test TaggedConsoleSceneView
¶<<
¶>>TaggedConsoleSceneView test execution time (ms):2<<
¶>>Test Result:
¶TaggedConsoleSceneView,100.0% complete,0.0,0.0,
¶<<
¶>>Wed Jul 09 21:59:56 EDT 2025<<
¶>>Running test ConsoleSceneViewGetsBridgeScene
¶<<
¶>>Wed Jul 09 21:59:56 EDT 2025<<
¶>>Running test TaggedLocatable
¶<<
¶>>Steps traced since last test:
¶
¶>>TaggedLocatable test execution time (ms):2"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="43" _type="ConsoleOutput" date="Wed Jul 09 21:59:56 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="158724" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Steps traced since last test:

>>ConsoleSceneViewGetsBridgeScene test execution time (ms):4<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 21:59:56 EDT 2025<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 21:59:56 EDT 2025<<
¶>>Running test TaggedConsoleSceneView
¶<<
¶>>Tagged"), Diff(INSERT,"Test Result:
¶TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
¶<<
¶>>Steps traced since last test:
¶
¶>>"), Diff(EQUAL,"ConsoleSceneView"), Diff(INSERT,"GetsBridgeScene"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"2"), Diff(INSERT,"4"), Diff(EQUAL,"<<
¶>>Test Result:
¶"), Diff(DELETE,"Tagged"), Diff(EQUAL,"ConsoleSceneView"), Diff(DELETE,",10"), Diff(INSERT,"GetsBridgeScene,"), Diff(EQUAL,"0.0% complete,0.0,0.0,
¶"), Diff(DELETE,"<<
¶>>Wed Jul 09 21:59:56 EDT 2025<<
¶>>Running test ConsoleSceneViewGetsBridgeScene"), Diff(INSERT,"Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test"), Diff(EQUAL,"
¶<<
¶>>Wed Jul 09 21:59:56 EDT 2025<<
¶>>Running test "), Diff(DELETE,"TaggedLocatable
¶<<
¶>>Steps traced since last test:
¶
¶>>TaggedLocatable test execution time (ms):2"), Diff(INSERT,"ConsoleSceneViewRegistersWithLocatables
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="44" _type="ConsoleOutput" date="Wed Jul 09 21:59:56 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="158737" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewRegistersWithLocatables test execution time (ms):1<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 21:59:56 EDT 2025<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):0<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 21:59:56 EDT 2025<<
>>Running test ConsoleSceneView
<<
>>ConsoleSceneView test execution time (ms):1<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶Tagged"), Diff(INSERT,"ConsoleSceneViewRegistersWithLocatables test execution time (ms):1<<
¶>>Test Result:
¶ConsoleSceneViewRegistersWith"), Diff(EQUAL,"Locatable"), Diff(INSERT,"s"), Diff(EQUAL,",0.0% complete,0.0,0.0,"), Diff(DELETE,"No class in project matching name/tag:Locatable
¶<<
¶>>Steps traced since last test:
¶
¶>>ConsoleSceneViewGetsBridgeSc"), Diff(INSERT,"
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 21:59:56 EDT 2025<<
¶>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEv"), Diff(EQUAL,"en"), Diff(DELETE,"e"), Diff(INSERT,"t"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"4"), Diff(INSERT,"0"), Diff(EQUAL,"<<
¶>>Test Result:
¶ConsoleSceneView"), Diff(DELETE,"GetsBridgeSc"), Diff(INSERT,"PrintsPropertyChangeEv"), Diff(EQUAL,"en"), Diff(DELETE,"e"), Diff(INSERT,"t"), Diff(EQUAL,",0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 21:59:56 EDT 2025<<
¶>>Running test ConsoleSceneView"), Diff(DELETE,"RegistersWithLocatables"), Diff(INSERT,"
¶<<
¶>>ConsoleSceneView test execution time (ms):1<<
¶>>Test Result:
¶ConsoleSceneView,0.0% complete,0.0,50.0,
¶Preceding test BridgeSceneSingletonFromFactory failed.
¶Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="45" _type="ConsoleOutput" date="Wed Jul 09 22:00:01 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="163110" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Observables
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"ConsoleSceneViewRegistersWithLocatables test execution time (ms):1<<
¶>>Test Result:
¶ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 21:59:56 EDT 2025<<
¶>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):0<<
¶>>Test Result:
¶ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 21:59:56 EDT 2025<<
¶>>Running test ConsoleSceneView
¶<<
¶>>ConsoleSceneView test execution time (ms):1<<
¶>>Test Result:
¶ConsoleSceneView,0.0% complete,0.0,50.0,
¶Preceding test BridgeSceneSingletonFromFactory failed.
¶Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test"), Diff(INSERT,"Running suite A2Observables"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="46" _type="ConsoleOutput" date="Wed Jul 09 22:00:01 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="163115" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:00:01 EDT 2025<<
>>Running test Locatable_IS_A_PropertyListenerRegisterer
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2Observables"), Diff(INSERT,"Wed Jul 09 22:00:01 EDT 2025<<
¶>>Running test Locatable_IS_A_PropertyListenerRegisterer"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="47" _type="ConsoleOutput" date="Wed Jul 09 22:00:01 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="163183" type="ConsoleOutput">
    <outputString><![CDATA[>>Locatable_IS_A_PropertyListenerRegisterer test execution time (ms):73<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:00:01 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"Locatable_IS_A_PropertyListenerRegisterer"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):73"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="48" _type="ConsoleOutput" date="Wed Jul 09 22:00:01 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="163189" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
Locatable_IS_A_PropertyListenerRegisterer,100.0% complete,0.0,0.0,
<<
>>Wed Jul 09 22:00:01 EDT 2025<<
>>Running test LocatablePropertyChangeListenersProperty
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"Locatable_IS_A_PropertyListenerRegisterer"), Diff(DELETE," test execution time (ms):73"), Diff(INSERT,",100.0% complete,0.0,0.0,
¶<<
¶>>Wed Jul 09 22:00:01 EDT 2025<<
¶>>Running test LocatablePropertyChangeListenersProperty
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="49" _type="ConsoleOutput" date="Wed Jul 09 22:00:01 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="163198" type="ConsoleOutput">
    <outputString><![CDATA[>>LocatablePropertyChangeListenersProperty test execution time (ms):1<<
>>Test Result:
LocatablePropertyChangeListenersProperty,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:00:01 EDT 2025<<
>>Running test LocatableInstantiatesPropertyChangeEvent
<<
>>LocatableInstantiatesPropertyChangeEvent test execution time (ms):1<<
>>Test Result:
LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:00:01 EDT 2025<<
>>Running test LocatableAnnouncesPropertyChangeEvent
<<
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,">>LocatablePropertyChangeListenersProperty test execution time (ms):1<<
¶"), Diff(EQUAL,">>Test Result:
¶Locatable"), Diff(DELETE,"_IS_A_"), Diff(EQUAL,"Property"), Diff(INSERT,"Change"), Diff(EQUAL,"Listener"), Diff(DELETE,"Registerer,10"), Diff(INSERT,"sProperty,"), Diff(EQUAL,"0.0% complete,0.0,0.0,
¶"), Diff(INSERT,"Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶"), Diff(EQUAL,"<<
¶>>Wed Jul 09 22:00:01 EDT 2025<<
¶>>Running test Locatable"), Diff(INSERT,"Instantiates"), Diff(EQUAL,"PropertyChange"), Diff(DELETE,"ListenersProperty"), Diff(INSERT,"Event
¶<<
¶>>LocatableInstantiatesPropertyChangeEvent test execution time (ms):1<<
¶>>Test Result:
¶LocatableInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:00:01 EDT 2025<<
¶>>Running test LocatableAnnouncesPropertyChangeEvent"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="50" _type="ConsoleOutput" date="Wed Jul 09 22:00:01 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="163202" type="ConsoleOutput">
    <outputString><![CDATA[>>LocatableAnnouncesPropertyChangeEvent test execution time (ms):0<<
>>Test Result:
LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:00:01 EDT 2025<<
>>Running test BoundedShapeInstantiatesPropertyChangeEvent
<<
>>BoundedShapeInstantiatesPropertyChangeEvent test execution time (ms):1<<
>>Test Result:
BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:00:01 EDT 2025<<
>>Running test BoundedShapeAnnouncesPropertyChangeEvent
<<
>>BoundedShapeAnnouncesPropertyChangeEvent test execution time (ms):1<<
>>Test Result:
BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Locatable"), Diff(INSERT,"Announces"), Diff(EQUAL,"PropertyChange"), Diff(DELETE,"ListenersProperty"), Diff(INSERT,"Event"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"1"), Diff(INSERT,"0"), Diff(EQUAL,"<<
¶>>Test Result:
¶Locatable"), Diff(INSERT,"Announces"), Diff(EQUAL,"PropertyChange"), Diff(DELETE,"ListenersProperty"), Diff(INSERT,"Event"), Diff(EQUAL,",0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:00:01 EDT 2025<<
¶>>Running test "), Diff(DELETE,"Locatabl"), Diff(INSERT,"BoundedShap"), Diff(EQUAL,"eInstantiatesPropertyChangeEvent
¶<<
¶>>"), Diff(DELETE,"Locatabl"), Diff(INSERT,"BoundedShap"), Diff(EQUAL,"eInstantiatesPropertyChangeEvent test execution time (ms):1<<
¶>>Test Result:
¶"), Diff(DELETE,"Locatabl"), Diff(INSERT,"BoundedShap"), Diff(EQUAL,"eInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:00:01 EDT 2025<<
¶>>Running test "), Diff(DELETE,"LocatableAnnouncesPropertyChangeEven"), Diff(INSERT,"BoundedShapeAnnouncesPropertyChangeEvent
¶<<
¶>>BoundedShapeAnnouncesPropertyChangeEvent test execution time (ms):1<<
¶>>Test Result:
¶BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this tes"), Diff(EQUAL,"t
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="51" _type="ConsoleOutput" date="Wed Jul 09 22:00:04 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="165891" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite BridgeSceneScroll
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"LocatableAnnouncesPropertyChangeEvent test execution time (ms):0<<
¶>>Test Result:
¶LocatableAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:00:01 EDT 2025<<
¶>>Running test BoundedShapeInstantiatesPropertyChangeEvent
¶<<
¶>>BoundedShapeInstantiatesPropertyChangeEvent test execution time (ms):1<<
¶>>Test Result:
¶BoundedShapeInstantiatesPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:00:01 EDT 2025<<
¶>>Running test BoundedShapeAnnouncesPropertyChangeEvent
¶<<
¶>>BoundedShapeAnnouncesPropertyChangeEvent test execution time (ms):1<<
¶>>Test Result:
¶BoundedShapeAnnouncesPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test"), Diff(INSERT,"Running suite BridgeSceneScroll"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="52" _type="ConsoleOutput" date="Wed Jul 09 22:00:04 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="165896" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:00:04 EDT 2025<<
>>Running test BridgeSceneScrollMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,">>Wed Jul 09 22:00:04 EDT 2025<<
¶"), Diff(EQUAL,">>Running "), Diff(DELETE,"sui"), Diff(EQUAL,"te"), Diff(INSERT,"st"), Diff(EQUAL," BridgeSceneScroll"), Diff(INSERT,"MethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="53" _type="ConsoleOutput" date="Wed Jul 09 22:00:04 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="165907" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneScrollMethodDefined test execution time (ms):20<<
>>Steps traced since last test:
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:00:04 EDT 2025<<
¶>>Running test BridgeSceneScrollMethodDefined
¶<<"), Diff(INSERT,"BridgeSceneScrollMethodDefined test execution time (ms):20<<
¶>>Steps traced since last test:"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="54" _type="ConsoleOutput" date="Wed Jul 09 22:00:04 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="165914" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneScrollMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:19: Missing signature @scroll:int;int->void//EC in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneScrollMethodDefined"), Diff(DELETE," test execution time (ms):20<<
¶>>Steps traced since last test:"), Diff(INSERT,",0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:19: Missing signature @scroll:int;int->void//EC in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="55" _type="ExceptionCommand" date="Wed Jul 09 22:00:04 EDT 2025" starttimestamp="1752112638210" timestamp="165916" type="Exception">
    <exceptionString><![CDATA[>>Wed Jul 09 22:00:04 EDT 2025<<
>>Running test BridgeSceneArthurScrollLeftArmTestCase
<<
java.lang.Error: Unresolved compilation problem: 
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)

	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
	at mp.bridge.Shape.move(Shape.java:37)
	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at grader.basics.execution.AConstructorExecutionCallable.call(AConstructorExecutionCallable.java:18)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
]]></exceptionString>
    <language><![CDATA[SML]]></language>
  </Command>
  <Command __id="56" _type="ConsoleOutput" date="Wed Jul 09 22:00:04 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="165929" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:00:04 EDT 2025<<
>>Running test BridgeSceneArthurScrollLeftArmTestCase
<<
java.lang.Error: Unresolved compilation problem: 
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)

	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
	at mp.bridge.Shape.move(Shape.java:37)
	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at grader.basics.execution.AConstructorExecutionCallable.call(AConstructorExecutionCallable.java:18)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 22:00:04 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"ScrollMethodDefined,0.0% complete,0.0,2.0,main\BridgeSceneImpl.java:19: Missing signature @scroll:int;int->void//EC in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. ["), Diff(INSERT,"ArthurScrollLeftArmTestCase
¶<<
¶java.lang.Error: Unresolved compilation problem: 
¶	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
¶
¶	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
¶	at mp.bridge.Shape.move(Shape.java:37)
¶	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
¶	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
¶	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
¶	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
¶	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
¶	at grader.basics.execution.AConstructorExecutionCallable.call(AConstructorExecutionCallable.java:18)
¶	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
¶	at java.base/java.util.concurrent.ThreadPool"), Diff(EQUAL,"Ex"), Diff(DELETE,"p"), Diff(EQUAL,"ec"), Diff(DELETE,"tedSignatures]
¶<<"), Diff(INSERT,"utor.runWorker(ThreadPoolExecutor.java:1136)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
¶	at java.base/java.lang.Thread.run(Thread.java:833)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="57" _type="ExceptionCommand" date="Wed Jul 09 22:00:04 EDT 2025" starttimestamp="1752112638210" timestamp="165940" type="Exception">
    <exceptionString><![CDATA[java.lang.NullPointerException
	at java.base/java.util.Hashtable.put(Hashtable.java:476)
	at util.models.Hashcodetable.put(Hashcodetable.java:15)
	at grader.basics.project.BasicProjectIntrospection.createTimingOutProxy(BasicProjectIntrospection.java:2341)
	at grader.basics.project.BasicProjectIntrospection.createProxy(BasicProjectIntrospection.java:2356)
	at grader.basics.project.BasicProjectIntrospection.createInstance(BasicProjectIntrospection.java:2503)
	at grader.basics.project.BasicProjectIntrospection.createInstance(BasicProjectIntrospection.java:2466)
	at grader.basics.project.BasicProjectIntrospection.createInstance(BasicProjectIntrospection.java:2260)
	at grader.basics.project.BasicProjectIntrospection.createOrGetLastInstance(BasicProjectIntrospection.java:2254)
	at gradingTools.shared.testcases.ProxyTest.createOrGetLastRootProxy(ProxyTest.java:70)
	at gradingTools.shared.testcases.ProxyTest.create(ProxyTest.java:181)
	at gradingTools.comp401f16.assignment5.testcases.move.arthur.BridgeSceneMoveTestCase.create(BridgeSceneMoveTestCase.java:28)
	at gradingTools.shared.testcases.ProxyTest.doProxyTest(ProxyTest.java:253)
	at gradingTools.shared.testcases.ProxyTest.doTest(ProxyTest.java:262)
	at gradingTools.comp401f16.assignment5.testcases.move.arthur.BridgeSceneArthurMoveLeftArmTestCase.doTest(BridgeSceneArthurMoveLeftArmTestCase.java:32)
	at gradingTools.comp401f16.assignment6.testcases.scroll.BridgeSceneArthurScrollLeftArmTestCase.doTest(BridgeSceneArthurScrollLeftArmTestCase.java:26)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at jdk.internal.reflect.GeneratedMethodAccessor28.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
]]></exceptionString>
    <language><![CDATA[java]]></language>
  </Command>
  <Command __id="58" _type="ExceptionCommand" date="Wed Jul 09 22:00:04 EDT 2025" starttimestamp="1752112638210" timestamp="165963" type="Exception">
    <exceptionString><![CDATA[>>BridgeSceneLancelotScrollLeftArmTestCase test execution time (ms):7<<
>>Test Result:
BridgeSceneLancelotScrollLeftArmTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
>>Wed Jul 09 22:00:04 EDT 2025<<
>>Running test BridgeSceneRobinScrollLeftArmTestCase
<<
java.lang.Error: Unresolved compilation problem: 
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)

	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
	at mp.bridge.Shape.move(Shape.java:37)
	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at grader.basics.execution.AConstructorExecutionCallable.call(AConstructorExecutionCallable.java:18)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
java.lang.NullPointerException
	at java.base/java.util.Hashtable.put(Hashtable.java:476)
	at util.models.Hashcodetable.put(Hashcodetable.java:15)
	at grader.basics.project.BasicProjectIntrospection.createTimingOutProxy(BasicProjectIntrospection.java:2341)
	at grader.basics.project.BasicProjectIntrospection.createProxy(BasicProjectIntrospection.java:2356)
	at grader.basics.project.BasicProjectIntrospection.createInstance(BasicProjectIntrospection.java:2503)
	at grader.basics.project.BasicProjectIntrospection.createInstance(BasicProjectIntrospection.java:2466)
	at grader.basics.project.BasicProjectIntrospection.createInstance(BasicProjectIntrospection.java:2260)
	at grader.basics.project.BasicProjectIntrospection.createOrGetLastInstance(BasicProjectIntrospection.java:2254)
	at gradingTools.shared.testcases.ProxyTest.createOrGetLastRootProxy(ProxyTest.java:70)
	at gradingTools.shared.testcases.ProxyTest.create(ProxyTest.java:181)
	at gradingTools.comp401f16.assignment5.testcases.move.arthur.BridgeSceneMoveTestCase.create(BridgeSceneMoveTestCase.java:28)
	at gradingTools.shared.testcases.ProxyTest.doProxyTest(ProxyTest.java:253)
	at gradingTools.shared.testcases.ProxyTest.doTest(ProxyTest.java:262)
	at gradingTools.comp401f16.assignment5.testcases.move.arthur.BridgeSceneArthurMoveLeftArmTestCase.doTest(BridgeSceneArthurMoveLeftArmTestCase.java:32)
	at gradingTools.comp401f16.assignment6.testcases.scroll.BridgeSceneArthurScrollLeftArmTestCase.doTest(BridgeSceneArthurScrollLeftArmTestCase.java:26)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at jdk.internal.reflect.GeneratedMethodAccessor28.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
]]></exceptionString>
    <language><![CDATA[java]]></language>
  </Command>
  <Command __id="59" _type="ConsoleOutput" date="Wed Jul 09 22:00:04 EDT 2025" overflow="false" starttimestamp="1752112638210" timestamp="166003" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneArthurScrollRightLegTestCase test execution time (ms):6<<
>>Test Result:
BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:00:04 EDT 2025<<
¶>>Running test BridgeSceneArthurScrollLeftArmTestCase
¶<<
¶java.lang.Error: Unresolved compilation problem: 
¶	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
¶
¶	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
¶	at mp.bridge.Shape.move(Shape.java:37)
¶	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
¶	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
¶	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
¶	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
¶	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
¶	at grader.basics.execution.AConstructorExecutionCallable.call(AConstructorExecutionCallable.java:18)
¶	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
¶	at java.base/java.lang.Thread.run(Thread.java:833)"), Diff(INSERT,"BridgeSceneArthurScrollRightLegTestCase test execution time (ms):6<<
¶>>Test Result:
¶BridgeSceneArthurScrollRightLegTestCase,0.0% complete,0.0,2.0,Could not find, or successfully call constructor, taking arguments [] See console message about NoSuch MethodException above stack trace
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="60" _type="ShellCommand" date="Wed Jul 09 22:00:10 EDT 2025" starttimestamp="1752112638210" timestamp="172041" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="61" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:00:10 EDT 2025" kind="HitBreakPoint" projectName="Assn2" starttimestamp="1752112638210" timestamp="172251" type="Run" />
  <Command __id="62" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:00:10 EDT 2025" kind="Terminate" projectName="Assn2" starttimestamp="1752112638210" timestamp="172251" type="Run" />
  <Command __id="63" _type="ShellCommand" date="Wed Jul 09 22:00:13 EDT 2025" starttimestamp="1752112638210" timestamp="175437" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="64" _type="ShellCommand" date="Wed Jul 09 22:00:48 EDT 2025" starttimestamp="1752112638210" timestamp="210392" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="65" _type="ShellCommand" date="Wed Jul 09 22:00:54 EDT 2025" starttimestamp="1752112638210" timestamp="216395" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="66" _type="ShellCommand" date="Wed Jul 09 22:04:01 EDT 2025" starttimestamp="1752112638210" timestamp="403019" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="67" _type="ShellCommand" date="Wed Jul 09 22:04:02 EDT 2025" starttimestamp="1752112638210" timestamp="404379" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="68" _type="ShellCommand" date="Wed Jul 09 22:05:54 EDT 2025" starttimestamp="1752112638210" timestamp="516461" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="69" _type="ShellCommand" date="Wed Jul 09 22:05:57 EDT 2025" starttimestamp="1752112638210" timestamp="518897" type="ECLIPSE_CLOSED" />
  <Command __id="6" _type="DiffBasedFileOpenCommand" date="Wed Jul 09 21:58:26 EDT 2025" docASTNodeCount="285" docActiveCodeLength="1722" docExpressionCount="171" docLength="1722" projectName="Assn2" starttimestamp="1752112638210" timestamp="68763">
    <filePath><![CDATA[C:\Users\<USER>\code\Java\Isa\Assn2\src\mp\bridge\AbstractStringShape.java]]></filePath>
    <diff><![CDATA[null]]></diff>
    <snapshot><![CDATA[package mp.bridge;

import mp.shapes.Locatable;
import util.annotations.Visible;
import util.models.PropertyListenerRegisterer;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.List;
import java.util.ArrayList;

public abstract class AbstractStringShape implements StringShape, PropertyListenerRegisterer {
    protected String text;
    protected int x, y;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();
    
    public AbstractStringShape(String defaultText) {
        this.text = defaultText;
        this.x = 0;
        this.y = 0;
    }
    
    @Override
    public String getText() {
        return text;
    }
    
    @Override
    public void setText(String text) {
        String oldText = this.text;
        this.text = text;
        notify("Text", oldText, text);
    }

    @Override
    public int getX() {
        return x;
    }

    @Override
    public void setX(int x) {
        int oldX = this.x;
        this.x = x;
        notify("X", oldX, x);
    }

    @Override
    public int getY() {
        return y;
    }

    @Override
    public void setY(int y) {
        int oldY = this.y;
        this.y = y;
        notify("Y", oldY, y);
    }

    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        propertyChangeListeners.add(listener);
    }

    @Visible(false)
    public void notify(String property, Object old, Object current) {
        PropertyChangeEvent event = new PropertyChangeEvent(this, property, old, current);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }
}
]]></snapshot>
  </Command>
</Events>
