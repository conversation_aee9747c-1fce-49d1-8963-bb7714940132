*PRE_OUTPUT*
>>Running suite A2Style
<<
>>Running test A2PackageDeclarations
<<
>>Running test BridgeSceneDynamics
<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running test BridgeSceneSayMethodDefined
<<
>>Running test BridgeScenePassedMethodDefined
<<
>>Running test BridgeSceneFailedMethodDefined
<<
>>Running test A2SimplifyBooleanExpressions
<<
>>Running test A2SimplifyBooleanReturns
<<
>>Running test A2NoHiddenFields
<<
>>Running test A2NamingConventions
<<
>>Running test A2InterfaceAsType
<<
>>Running test A2NamedConstants
<<
>>Running test A2NoStarImports
<<
>>Running test A2PublicMethodsOverride
<<
>>Running test A2MnemonicNames
<<
>>Running test A2Encapsulation
<<
>>Running test A2NonPublicAccessModifiersMatched
<<
>>Running test A2CommonPropertiesAreInherited
<<
>>Running test A2CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. 
<<
>>Test Result:
LocatableXProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableYProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableXEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableYEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,No class in project matching name/tag:BoundedShape
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test LocatableXProperty failed.
Please correct the problems identified by preceding test:LocatableXProperty before running this test
<<
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,0.0% complete,0.0,5.0,mp\bridge\AbstractImageShape.java:45:26: 'x' hides a field. [HiddenField]mp\bridge\AbstractImageShape.java:57:26: 'y' hides a field. [HiddenField]mp\bridge\AbstractStringShape.java:27:32: 'text' hides a field. [HiddenField]mp\bridge\AbstractStringShape.java:39:26: 'x' hides a field. [HiddenField]mp\bridge\AbstractStringShape.java:51:26: 'y' hides a field. [HiddenField]mp\shapes\AbstractAScaleRectangle.java:20:40: 'x' hides a field. [HiddenField]mp\shapes\AbstractAScaleRectangle.java:20:47: 'y' hides a field. [HiddenField]mp\shapes\AbstractAScaleRectangle.java:20:54: 'width' hides a field. [HiddenField]mp\shapes\AbstractAScaleRectangle.java:20:65: 'height' hides a field. [HiddenField]mp\shapes\AbstractAScaleRectangle.java:33:26: 'x' hides a field. [HiddenField]mp\shapes\AbstractAScaleRectangle.java:45:26: 'y' hides a field. [HiddenField]mp\shapes\AbstractAScaleRectangle.java:57:30: 'width' hides a field. [HiddenField]mp\shapes\AbstractAScaleRectangle.java:69:31: 'height' hides a field. [HiddenField]mp\shapes\AbstractBoundedShape.java:13:51: 'width' hides a field. [HiddenField]mp\shapes\AbstractBoundedShape.java:13:62: 'height' hides a field. [HiddenField]mp\shapes\AbstractBoundedShape.java:25:30: 'width' hides a field. [HiddenField]mp\shapes\AbstractBoundedShape.java:37:31: 'height' hides a field. [HiddenField]mp\shapes\AbstractLocatable.java:24:34: 'x' hides a field. [HiddenField]mp\shapes\AbstractLocatable.java:24:41: 'y' hides a field. [HiddenField]mp\shapes\AbstractLocatable.java:35:26: 'x' hides a field. [HiddenField]mp\shapes\AbstractLocatable.java:47:26: 'y' hides a field. [HiddenField]mp\shapes\AbstractPolarPoint.java:12:38: 'radius' hides a field. [HiddenField]mp\shapes\AbstractPolarPoint.java:12:53: 'angle' hides a field. [HiddenField]mp\shapes\AbstractPolarPoint.java:30:34: 'radius' hides a field. [HiddenField]mp\shapes\AbstractPolarPoint.java:43:33: 'angle' hides a field. [HiddenField]mp\shapes\AbstractRotateLine.java:29:26: 'x' hides a field. [HiddenField]mp\shapes\AbstractRotateLine.java:40:26: 'y' hides a field. [HiddenField]mp\shapes\ALocatable.java:22:27: 'x' hides a field. [HiddenField]mp\shapes\ALocatable.java:22:34: 'y' hides a field. [HiddenField]mp\shapes\ALocatable.java:33:26: 'x' hides a field. [HiddenField]mp\shapes\ALocatable.java:43:26: 'y' hides a field. [HiddenField]
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,98.61111111111111% complete,4.9,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NamedConstants,90.47619047619048% complete,9.0,10.0,See console trace about lines failing  this check
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,50.0% complete,2.5,5.0,See console trace about lines failing  this check
<<
>>Test Result:
A2MnemonicNames,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,11.971830985915492% complete,0.8,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,87.82608695652175% complete,6.1,7.0,See console trace about lines failing  this check
<<
*END_OUTPUT*
