*PRE_OUTPUT*
>>Running suite A2Inheritance
<<
>>Running test TaggedLocatable
<<
>>Running test LocatableXProperty
<<
>>Running test LocatableYProperty
<<
>>Running test LocatableXEditableProperty
<<
>>Running test LocatableYEditableProperty
<<
>>Running test TaggedBoundedShape
<<
>>Running test BoundedShapeWidthProperty
<<
>>Running test BoundedShapeHeightProperty
<<
>>Running test BoundedShapeWidthEditableProperty
<<
>>Running test BoundedShapeHeightEditableProperty
<<
>>Running test A2ExpectedSuperTypes
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>Test Result:
LocatableXProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableYProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableXEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
LocatableYEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,Class class mp.shapes.AScaleRectangle with tag BoundedShape does not have an interface
<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test LocatableXProperty failed.
Please correct the problems identified by preceding test:LocatableXProperty before running this test
<<
*END_OUTPUT*
