package mp.bridge;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.PropertyNames;
import util.annotations.EditablePropertyNames;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
@PropertyNames({"X", "Y", "ImageFileName"})
@EditablePropertyNames({"X", "Y", "ImageFileName"})
public class GalahadHead extends AbstractImageShape{
    public GalahadHead() {
        super("images/galahad.jpg");
    }
}
