<Events startTimestamp="1752113184173" logVersion="1.0.0.202503121800">
  <Command __id="10" _type="EclipseCommand" commandID="" date="Wed Jul 09 22:06:35 EDT 2025" starttimestamp="1752113184173" timestamp="10907" />
  <Command __id="11" _type="ShellCommand" date="Wed Jul 09 22:06:41 EDT 2025" starttimestamp="1752113184173" timestamp="16836" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="17" _type="RunCommand" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:06:46 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752113184173" timestamp="22603" type="Run" />
  <Command __id="18" _type="ShellCommand" date="Wed Jul 09 22:06:50 EDT 2025" starttimestamp="1752113184173" timestamp="26448" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="19" _type="ConsoleOutput" date="Wed Jul 09 22:07:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="58358" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Factory
<<
]]></outputString>
    <diff><![CDATA[null]]></diff>
  </Command>
  <Command __id="20" _type="ConsoleOutput" date="Wed Jul 09 22:07:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="58413" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:07:22 EDT 2025<<
>>Running test TaggedFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2"), Diff(INSERT,"Wed Jul 09 22:07:22 EDT 2025<<
¶>>Running test Tagged"), Diff(EQUAL,"Factory
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="21" _type="ConsoleOutput" date="Wed Jul 09 22:07:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="58425" type="ConsoleOutput">
    <outputString><![CDATA[>>TaggedFactory test execution time (ms):37<<
>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:07:22 EDT 2025<<
>>Running test BridgeSceneFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,">>TaggedFactory test execution time (ms):37<<
¶>>Test Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶<<
¶"), Diff(EQUAL,">>Wed Jul 09 22:07:22 EDT 2025<<
¶>>Running test "), Diff(DELETE,"TaggedFactory"), Diff(INSERT,"BridgeSceneFactoryMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="22" _type="ConsoleOutput" date="Wed Jul 09 22:07:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="58431" type="ConsoleOutput">
    <outputString><![CDATA[>>Running checkstyle, this will take time<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"TaggedFactory test execution time (ms):37<<
¶>>Test Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:07:22 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined
¶"), Diff(INSERT,"Running checkstyle, this will take time"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="23" _type="ConsoleOutput" date="Wed Jul 09 22:07:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="58473" type="ConsoleOutput">
    <outputString><![CDATA[WARNING: A terminally deprecated method in java.lang.System has been called
WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
WARNING: System::setSecurityManager will be removed in a future release
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Running checkstyle, this will take time<<"), Diff(INSERT,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="24" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62267" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneFactoryMethodDefined test execution time (ms):3836<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(INSERT,">>BridgeSceneFactoryMethodDefined test execution time (ms):3836<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="25" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62273" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneFactoryMethodDefined"), Diff(DELETE," test execution time (ms):3836"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="26" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62278" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:07:26 EDT 2025<<
>>Running test BridgeSceneSingletonFromFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"FactoryMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"SingletonFromFactory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="27" _type="ExceptionCommand" date="Wed Jul 09 22:07:26 EDT 2025" starttimestamp="1752113184173" timestamp="62282" type="Exception">
    <exceptionString><![CDATA[Execution exception caused by invocation exception caused by:
java.lang.Error: Unresolved compilation problem: 
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
]]></exceptionString>
    <language><![CDATA[SML]]></language>
  </Command>
  <Command __id="28" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62285" type="ConsoleOutput">
    <outputString><![CDATA[Execution exception caused by invocation exception caused by:
java.lang.Error: Unresolved compilation problem: 
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test BridgeSceneSingletonFromFactory
¶<<"), Diff(INSERT,"Execution exception caused by invocation exception caused by:
¶java.lang.Error: Unresolved compilation problem: 
¶	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="29" _type="ExceptionCommand" date="Wed Jul 09 22:07:26 EDT 2025" starttimestamp="1752113184173" timestamp="62291" type="Exception">
    <exceptionString><![CDATA[	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
	at mp.bridge.Shape.move(Shape.java:37)
	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
java.lang.Error: Unresolved compilation problem: 
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)

	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
	at mp.bridge.Shape.move(Shape.java:37)
	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
java.lang.AssertionError: Factory method returns null object%0.0
	at org.junit.Assert.fail(Assert.java:88)
	at org.junit.Assert.assertTrue(Assert.java:41)
	at grader.basics.testcase.PassFailJUnitTestCase.assertTrue(PassFailJUnitTestCase.java:339)
	at gradingTools.shared.testcases.FactoryMethodTest.maybeUseConstructor(FactoryMethodTest.java:144)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:411)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:316)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
]]></exceptionString>
    <language><![CDATA[SML]]></language>
  </Command>
  <Command __id="30" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62327" type="ConsoleOutput">
    <outputString><![CDATA[	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
	at mp.bridge.Shape.move(Shape.java:37)
	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
java.lang.Error: Unresolved compilation problem: 
	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)

	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
	at mp.bridge.Shape.move(Shape.java:37)
	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
java.lang.AssertionError: Factory method returns null object%0.0
	at org.junit.Assert.fail(Assert.java:88)
	at org.junit.Assert.assertTrue(Assert.java:41)
	at grader.basics.testcase.PassFailJUnitTestCase.assertTrue(PassFailJUnitTestCase.java:339)
	at gradingTools.shared.testcases.FactoryMethodTest.maybeUseConstructor(FactoryMethodTest.java:144)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:411)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:316)
	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"Execution exception caused by invocation exception caused by:
¶java.lang.Error: Unresolved compilation problem: 
¶	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int"), Diff(INSERT,"	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
¶	at mp.bridge.Shape.move(Shape.java:37)
¶	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
¶	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
¶	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
¶	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
¶	at java.base/java.lang.Thread.run(Thread.java:833)
¶java.lang.Error: Unresolved compilation problem: 
¶	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
¶
¶	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
¶	at mp.bridge.Shape.move(Shape.java:37)
¶	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
¶	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
¶	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
¶	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
¶	at java.base/java.lang.Thread.run(Thread.java:833)
¶java.lang.AssertionError: Factory method returns null object%0.0
¶	at org.junit.Assert.fail(Assert.java:88)
¶	at org.junit.Assert.assertTrue(Assert.java:41)
¶	at grader.basics.testcase.PassFailJUnitTestCase.assertTrue(PassFailJUnitTestCase.java:339)
¶	at gradingTools.shared.testcases.FactoryMethodTest.maybeUseConstructor(FactoryMethodTest.java:144)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:411)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:316)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
¶	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
¶	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50"), Diff(EQUAL,")
¶")]]]></diff>
  </Command>
  <Command __id="31" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62411" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneSingletonFromFactory test execution time (ms):15<<
>>Test Result:
BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory method returns null object
<<
>>Wed Jul 09 22:07:26 EDT 2025<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
¶	at mp.bridge.Shape.move(Shape.java:37)
¶	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
¶	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
¶	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
¶	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
¶	at java.base/java.lang.Thread.run(Thread.java:833)
¶java.lang.Error: Unresolved compilation problem: 
¶	The type RotatingLine must implement the inherited abstract method Moveable.move(int, int)
¶
¶	at mp.shapes.RotatingLine.move(RotatingLine.java:18)
¶	at mp.bridge.Shape.move(Shape.java:37)
¶	at mp.bridge.AvatarImpl.move(AvatarImpl.java:47)
¶	at main.BridgeSceneImpl.<init>(BridgeSceneImpl.java:53)
¶	at main.StaticFactoryClass.bridgeSceneFactoryMethod(StaticFactoryClass.java:15)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at grader.basics.execution.AMethodExecutionCallable.call(AMethodExecutionCallable.java:22)
¶	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
¶	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
¶	at java.base/java.lang.Thread.run(Thread.java:833)
¶java.lang.AssertionError: Factory method returns null object%0.0
¶	at org.junit.Assert.fail(Assert.java:88)
¶	at org.junit.Assert.assertTrue(Assert.java:41)
¶	at grader.basics.testcase.PassFailJUnitTestCase.assertTrue(PassFailJUnitTestCase.java:339)
¶	at gradingTools.shared.testcases.FactoryMethodTest.maybeUseConstructor(FactoryMethodTest.java:144)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:411)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:316)
¶	at gradingTools.shared.testcases.FactoryMethodTest.getObjectFromFactory(FactoryMethodTest.java:247)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactoryClassAndMethodTags(FactoryMethodTest.java:450)
¶	at gradingTools.shared.testcases.FactoryMethodTest.createUsingFactory(FactoryMethodTest.java:445)
¶	at gradingTools.shared.testcases.FactoryMethodTest.doFactoryMethodTest(FactoryMethodTest.java:126)
¶	at gradingTools.comp401f16.assignment7.testcases.factory.BridgeSceneFactoryMethodTest.doTest(BridgeSceneFactoryMethodTest.java:37)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)"), Diff(INSERT,">>BridgeSceneSingletonFromFactory test execution time (ms):15<<
¶>>Test Result:
¶BridgeSceneSingletonFromFactory,0.0% complete,0.0,2.0,Factory method returns null object
¶<<
¶>>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test ConsoleSceneViewFactoryMethodDefined
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="32" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62437" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewFactoryMethodDefined test execution time (ms):108<<
>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:07:26 EDT 2025<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
>>ConsoleSceneViewSingletonFromFactory test execution time (ms):3<<
>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:07:26 EDT 2025<<
>>Running test LegsFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"BridgeScene"), Diff(INSERT,"ConsoleSceneViewFactoryMethodDefined test execution time (ms):108<<
¶>>Test Result:
¶ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test ConsoleSceneViewSingletonFromFactory
¶<<
¶>>ConsoleSceneView"), Diff(EQUAL,"SingletonFromFactory test execution time (ms):"), Diff(DELETE,"15"), Diff(INSERT,"3"), Diff(EQUAL,"<<
¶>>Test Result:
¶"), Diff(DELETE,"Bridg"), Diff(INSERT,"Consol"), Diff(EQUAL,"eScene"), Diff(INSERT,"View"), Diff(EQUAL,"SingletonFromFactory,"), Diff(INSERT,"10"), Diff(EQUAL,"0.0% complete,"), Diff(DELETE,"0"), Diff(INSERT,"2"), Diff(EQUAL,".0,2.0,"), Diff(DELETE,"Factory method returns null object"), Diff(EQUAL,"
¶<<
¶>>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test "), Diff(DELETE,"ConsoleSceneView"), Diff(INSERT,"Legs"), Diff(EQUAL,"FactoryMethodDefined
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="33" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62515" type="ConsoleOutput">
    <outputString><![CDATA[>>LegsFactoryMethodDefined test execution time (ms):101<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"ConsoleSceneView"), Diff(INSERT,"Legs"), Diff(EQUAL,"FactoryMethodDefined test execution time (ms):10"), Diff(DELETE,"8<<
¶>>Test Result:
¶ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test ConsoleSceneViewSingletonFromFactory
¶<<
¶>>ConsoleSceneViewSingletonFromFactory test execution time (ms):3<<
¶>>Test Result:
¶ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test LegsFactoryMethodDefined
¶"), Diff(INSERT,"1"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="34" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62522" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:07:26 EDT 2025<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"LegsFactoryMethodDefined"), Diff(DELETE," test execution time (ms):101"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test A2MainCallsBridgeSceneFactoryMethod
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="35" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62562" type="ConsoleOutput">
    <outputString><![CDATA[>>A2MainCallsBridgeSceneFactoryMethod test execution time (ms):42<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):42"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="36" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62572" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE," test execution time (ms):42"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="37" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62583" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:07:26 EDT 2025<<
>>Running test AvatarCallsLegFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="38" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62624" type="ConsoleOutput">
    <outputString><![CDATA[>>AvatarCallsLegFactoryMethod test execution time (ms):51<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:07:26 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod
¶"), Diff(INSERT,"AvatarCallsLegFactoryMethod test execution time (ms):51"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="39" _type="ConsoleOutput" date="Wed Jul 09 22:07:26 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="62638" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"AvatarCallsLegFactoryMethod"), Diff(DELETE," test execution time (ms):51"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="40" _type="ConsoleOutput" date="Wed Jul 09 22:07:35 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="71560" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2ConsoleSceneView
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,"), Diff(INSERT,"Running suite A2ConsoleSceneView"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="41" _type="ConsoleOutput" date="Wed Jul 09 22:07:35 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="71567" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:07:35 EDT 2025<<
>>Running test TaggedConsoleSceneView
<<
>>TaggedConsoleSceneView test execution time (ms):0<<
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Wed Jul 09 22:07:35 EDT 2025<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Wed Jul 09 22:07:35 EDT 2025<<
>>Running test TaggedLocatable
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2ConsoleSceneView"), Diff(INSERT,"Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test TaggedConsoleSceneView
¶<<
¶>>TaggedConsoleSceneView test execution time (ms):0<<
¶>>Test Result:
¶TaggedConsoleSceneView,100.0% complete,0.0,0.0,
¶<<
¶>>Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test ConsoleSceneViewGetsBridgeScene
¶<<
¶>>Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test TaggedLocatable"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="42" _type="ConsoleOutput" date="Wed Jul 09 22:07:35 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="71571" type="ConsoleOutput">
    <outputString><![CDATA[>>Steps traced since last test:

>>TaggedLocatable test execution time (ms):2<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test TaggedConsoleSceneView
¶<<
¶>>TaggedConsoleSceneView"), Diff(INSERT,"Steps traced since last test:
¶
¶>>TaggedLocatable"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"0"), Diff(INSERT,"2"), Diff(EQUAL,"<<
¶>>Test Result:
¶Tagged"), Diff(DELETE,"ConsoleSceneView,10"), Diff(INSERT,"Locatable,"), Diff(EQUAL,"0.0% complete,0.0,0.0,"), Diff(DELETE,"
¶<<
¶>>Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test ConsoleSceneViewGetsBridgeScene
¶<<
¶>>Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test Tagged"), Diff(INSERT,"No class in project matching name/tag:"), Diff(EQUAL,"Locatable
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="43" _type="ConsoleOutput" date="Wed Jul 09 22:07:35 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="71576" type="ConsoleOutput">
    <outputString><![CDATA[>>Steps traced since last test:

>>ConsoleSceneViewGetsBridgeScene test execution time (ms):4<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:07:35 EDT 2025<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>ConsoleSceneViewRegistersWithLocatables test execution time (ms):0<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Steps traced since last test:
¶
¶>>"), Diff(DELETE,"TaggedLocatable"), Diff(INSERT,"ConsoleSceneViewGetsBridgeScene test execution time (ms):4<<
¶>>Test Result:
¶ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test ConsoleSceneViewRegistersWithLocatables
¶<<
¶>>ConsoleSceneViewRegistersWithLocatables"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"2"), Diff(INSERT,"0"), Diff(EQUAL,"<<
¶>>Test Result:
¶"), Diff(DELETE,"Tagged"), Diff(INSERT,"ConsoleSceneViewRegistersWith"), Diff(EQUAL,"Locatable"), Diff(INSERT,"s"), Diff(EQUAL,",0.0% complete,0.0,0.0,"), Diff(DELETE,"No class in project matching name/tag:Locatable"), Diff(INSERT,"
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="44" _type="ConsoleOutput" date="Wed Jul 09 22:07:35 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="71579" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:07:35 EDT 2025<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):1<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Steps traced since last test:
¶
¶>>ConsoleSceneViewGetsBridgeScene test execution time (ms):4<<
¶>>Test Result:
¶ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶"), Diff(EQUAL,">>Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test ConsoleSceneView"), Diff(DELETE,"RegistersWithLocatables
¶<<
¶>>ConsoleSceneViewRegistersWithLocatables"), Diff(INSERT,"PrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEvent"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"0"), Diff(INSERT,"1"), Diff(EQUAL,"<<
¶>>Test Result:
¶ConsoleSceneView"), Diff(DELETE,"RegistersWithLocatables"), Diff(INSERT,"PrintsPropertyChangeEvent"), Diff(EQUAL,",0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="45" _type="ConsoleOutput" date="Wed Jul 09 22:07:35 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="71584" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:07:35 EDT 2025<<
>>Running test ConsoleSceneView
<<
>>ConsoleSceneView test execution time (ms):0<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,
Preceding test BridgeSceneSingletonFromFactory failed.
Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test ConsoleSceneView"), Diff(DELETE,"PrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEvent"), Diff(INSERT,"
¶<<
¶>>ConsoleSceneView"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"1"), Diff(INSERT,"0"), Diff(EQUAL,"<<
¶>>Test Result:
¶ConsoleSceneView"), Diff(DELETE,"PrintsPropertyChangeEvent"), Diff(EQUAL,",0.0% complete,0.0,"), Diff(INSERT,"5"), Diff(EQUAL,"0.0,
¶Preceding test "), Diff(DELETE,"TaggedLocatable"), Diff(INSERT,"BridgeSceneSingletonFromFactory"), Diff(EQUAL," failed.
¶Please correct the problems identified by preceding test:"), Diff(DELETE,"TaggedLocatable"), Diff(INSERT,"BridgeSceneSingletonFromFactory"), Diff(EQUAL," before running this test
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="48" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:07:43 EDT 2025" kind="Terminate" projectName="Assn2" starttimestamp="1752113184173" timestamp="79232" type="Run" />
  <Command __id="50" _type="MoveCaretCommand" caretOffset="0" date="Wed Jul 09 22:07:47 EDT 2025" docOffset="0" starttimestamp="1752113184173" timestamp="82892" />
  <Command __id="51" _type="ShellCommand" date="Wed Jul 09 22:07:49 EDT 2025" starttimestamp="1752113184173" timestamp="85686" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="52" _type="ShellCommand" date="Wed Jul 09 22:19:47 EDT 2025" starttimestamp="1752113184173" timestamp="802870" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="53" _type="EclipseCommand" commandID="org.eclipse.ui.file.refresh" date="Wed Jul 09 22:19:51 EDT 2025" starttimestamp="1752113184173" timestamp="807794" />
  <Command __id="54" _type="EclipseCommand" commandID="" date="Wed Jul 09 22:19:52 EDT 2025" starttimestamp="1752113184173" timestamp="808033" />
  <Command __id="55" _type="EclipseCommand" commandID="" date="Wed Jul 09 22:19:52 EDT 2025" starttimestamp="1752113184173" timestamp="808161" />
  <Command __id="56" _type="ShellCommand" date="Wed Jul 09 22:19:55 EDT 2025" starttimestamp="1752113184173" timestamp="811478" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="62" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:20:05 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752113184173" timestamp="821726" type="Run" />
  <Command __id="63" _type="RunCommand" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:20:05 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752113184173" timestamp="821726" type="Run" />
  <Command __id="64" _type="ShellCommand" date="Wed Jul 09 22:20:09 EDT 2025" starttimestamp="1752113184173" timestamp="825590" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="65" _type="ConsoleOutput" date="Wed Jul 09 22:20:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="833893" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Factory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:07:35 EDT 2025<<
¶>>Running test ConsoleSceneView
¶<<
¶>>ConsoleSceneView test execution time (ms):0<<
¶>>Test Result:
¶ConsoleSceneView,0.0% complete,0.0,50.0,
¶Preceding test BridgeSceneSingletonFromFactory failed.
¶Please correct the problems identified by preceding test:BridgeSceneSingletonFromFactory before running this test"), Diff(INSERT,"Running suite A2Factory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="66" _type="ConsoleOutput" date="Wed Jul 09 22:20:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="833931" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:18 EDT 2025<<
>>Running test TaggedFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2"), Diff(INSERT,"Wed Jul 09 22:20:18 EDT 2025<<
¶>>Running test Tagged"), Diff(EQUAL,"Factory
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="67" _type="ConsoleOutput" date="Wed Jul 09 22:20:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="833951" type="ConsoleOutput">
    <outputString><![CDATA[>>TaggedFactory test execution time (ms):37<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:20:18 EDT 2025<<
¶>>Running test TaggedFactory
¶"), Diff(INSERT,"TaggedFactory test execution time (ms):37"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="68" _type="ConsoleOutput" date="Wed Jul 09 22:20:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="833957" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>T"), Diff(DELETE,"aggedFactory test execution time (ms):37"), Diff(INSERT,"est Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="69" _type="ConsoleOutput" date="Wed Jul 09 22:20:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="833960" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:18 EDT 2025<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running checkstyle, this will take time<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶"), Diff(INSERT,"Wed Jul 09 22:20:18 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined
¶<<
¶>>Running checkstyle, this will take time"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="70" _type="ConsoleOutput" date="Wed Jul 09 22:20:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="834002" type="ConsoleOutput">
    <outputString><![CDATA[WARNING: A terminally deprecated method in java.lang.System has been called
WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
WARNING: System::setSecurityManager will be removed in a future release
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Wed Jul 09 22:20:18 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined
¶<<
¶>>Running checkstyle, this will take time<<"), Diff(INSERT,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="71" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837835" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneFactoryMethodDefined test execution time (ms):3876<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(INSERT,">>BridgeSceneFactoryMethodDefined test execution time (ms):3876<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="72" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837842" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneFactoryMethodDefined"), Diff(DELETE," test execution time (ms):3876"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="73" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837852" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:22 EDT 2025<<
>>Running test BridgeSceneSingletonFromFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"FactoryMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"SingletonFromFactory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="74" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837872" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneSingletonFromFactory test execution time (ms):20<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeSceneSingletonFromFactory"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):20"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="75" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837875" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneSingletonFromFactory"), Diff(DELETE," test execution time (ms):20"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="76" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837877" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:22 EDT 2025<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test ConsoleSceneViewFactoryMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="77" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837903" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewFactoryMethodDefined test execution time (ms):27<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"ConsoleSceneViewFactoryMethodDefined"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):27"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="78" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837912" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:20:22 EDT 2025<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"ConsoleSceneViewFactoryMethodDefined"), Diff(DELETE," test execution time (ms):27"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test ConsoleSceneViewSingletonFromFactory
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="79" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837916" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewSingletonFromFactory test execution time (ms):3<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(EQUAL,"ConsoleSceneView"), Diff(DELETE,"FactoryMethodDefined,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test ConsoleSceneViewSingletonFromFactory
¶"), Diff(INSERT,"SingletonFromFactory test execution time (ms):3"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="80" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837918" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"ConsoleSceneViewSingletonFromFactory"), Diff(DELETE," test execution time (ms):3"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="81" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837920" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:22 EDT 2025<<
>>Running test LegsFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test LegsFactoryMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="82" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837944" type="ConsoleOutput">
    <outputString><![CDATA[>>LegsFactoryMethodDefined test execution time (ms):26<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test LegsFactoryMethodDefined
¶"), Diff(INSERT,"LegsFactoryMethodDefined test execution time (ms):26"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="83" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837948" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"LegsFactoryMethodDefined"), Diff(DELETE," test execution time (ms):26"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="84" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837949" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:22 EDT 2025<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶LegsFactoryMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test A2MainCallsBridgeSceneFactoryMethod"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="85" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837976" type="ConsoleOutput">
    <outputString><![CDATA[>>A2MainCallsBridgeSceneFactoryMethod test execution time (ms):27<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):27"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="86" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="837986" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:20:22 EDT 2025<<
>>Running test AvatarCallsLegFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE," test execution time (ms):27"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="87" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="838009" type="ConsoleOutput">
    <outputString><![CDATA[>>AvatarCallsLegFactoryMethod test execution time (ms):28<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:20:22 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod
¶"), Diff(INSERT,"AvatarCallsLegFactoryMethod test execution time (ms):28"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="88" _type="ConsoleOutput" date="Wed Jul 09 22:20:22 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="838011" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"AvatarCallsLegFactoryMethod"), Diff(DELETE," test execution time (ms):28"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="89" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845480" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2ConsoleSceneView
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,"), Diff(INSERT,"Running suite A2ConsoleSceneView"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="90" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845487" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:29 EDT 2025<<
>>Running test TaggedConsoleSceneView
<<
>>TaggedConsoleSceneView test execution time (ms):2<<
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Wed Jul 09 22:20:29 EDT 2025<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2"), Diff(INSERT,"Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test TaggedConsoleSceneView
¶<<
¶>>TaggedConsoleSceneView test execution time (ms):2<<
¶>>Test Result:
¶TaggedConsoleSceneView,100.0% complete,0.0,0.0,
¶<<
¶>>Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"ConsoleSceneView"), Diff(INSERT,"GetsBridgeScene"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="91" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845489" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:20:29 EDT 2025<<
>>Running test TaggedLocatable
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test Tagged"), Diff(DELETE,"ConsoleSceneView
¶<<
¶>>TaggedConsoleSceneView test execution time (ms):2<<
¶>>Test Result:
¶TaggedConsoleSceneView,100.0% complete,0.0,0.0,
¶<<
¶>>Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test ConsoleSceneViewGetsBridgeScen"), Diff(INSERT,"Locatabl"), Diff(EQUAL,"e
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="92" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845491" type="ConsoleOutput">
    <outputString><![CDATA[>>Steps traced since last test:
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test TaggedLocatable
¶<<"), Diff(INSERT,"Steps traced since last test:"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="93" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845494" type="ConsoleOutput">
    <outputString><![CDATA[>>TaggedLocatable test execution time (ms):4<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Steps traced since last test:"), Diff(INSERT,"TaggedLocatable test execution time (ms):4<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="94" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845496" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,Class class mp.bridge.AbstractStringShape with tag Locatable has multiple interfaces [interface mp.bridge.StringShape, interface util.models.PropertyListenerRegisterer]
<<
>>ConsoleSceneViewGetsBridgeScene test execution time (ms):7<<
>>Steps traced since last test:
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>T"), Diff(DELETE,"aggedLocatabl"), Diff(INSERT,"est Result:
¶TaggedLocatable,0.0% complete,0.0,0.0,Class class mp.bridge.AbstractStringShape with tag Locatable has multiple interfaces [interface mp.bridge.StringShape, interface util.models.PropertyListenerRegisterer]
¶<<
¶>>ConsoleSceneViewGetsBridgeScen"), Diff(EQUAL,"e test execution time (ms):"), Diff(DELETE,"4"), Diff(INSERT,"7"), Diff(EQUAL,"<<
¶"), Diff(INSERT,">>Steps traced since last test:
¶")]]]></diff>
  </Command>
  <Command __id="95" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845499" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:20:29 EDT 2025<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Test Result:
¶"), Diff(DELETE,"TaggedLocatable,0.0% complete,0.0,0.0,Class class mp.bridge.AbstractStringShape with tag Locatable has multiple interfaces [interface mp.bridge.StringShape, interface util.models.PropertyListenerReg"), Diff(INSERT,"ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running th"), Diff(EQUAL,"is"), Diff(INSERT," "), Diff(EQUAL,"te"), Diff(DELETE,"rer]"), Diff(INSERT,"st"), Diff(EQUAL,"
¶<<
¶>>"), Diff(DELETE,"ConsoleSceneViewGetsBridgeScene test execution time (ms):7<<
¶>>Steps traced since last test:"), Diff(INSERT,"Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test ConsoleSceneViewRegistersWithLocatables
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="96" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845501" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewRegistersWithLocatables test execution time (ms):1<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:20:29 EDT 2025<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):0<<
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,">>ConsoleSceneViewRegistersWithLocatables test execution time (ms):1<<
¶"), Diff(EQUAL,">>Test Result:
¶ConsoleSceneView"), Diff(DELETE,"GetsBridgeScene"), Diff(INSERT,"RegistersWithLocatables"), Diff(EQUAL,",0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test ConsoleSceneView"), Diff(DELETE,"RegistersWithLocatables
¶"), Diff(INSERT,"PrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):0"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="97" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845504" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:20:29 EDT 2025<<
>>Running test ConsoleSceneView
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>ConsoleSceneViewRegistersWithLocatables test execution time (ms):1<<
¶"), Diff(EQUAL,">>Test Result:
¶ConsoleSceneView"), Diff(DELETE,"RegistersWithLocatables"), Diff(INSERT,"PrintsPropertyChangeEvent"), Diff(EQUAL,",0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test ConsoleSceneView"), Diff(DELETE,"PrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):0"), Diff(INSERT,"
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="98" _type="ExceptionCommand" date="Wed Jul 09 22:20:29 EDT 2025" starttimestamp="1752113184173" timestamp="845535" type="Exception">
    <exceptionString><![CDATA[java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0
]]></exceptionString>
    <language><![CDATA[SML]]></language>
  </Command>
  <Command __id="99" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845536" type="ConsoleOutput">
    <outputString><![CDATA[java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Test Result:
¶ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:20:29 EDT 2025<<
¶>>Running test ConsoleSceneView
¶<<"), Diff(INSERT,"java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="100" _type="ConsoleOutput" date="Wed Jul 09 22:20:29 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="845560" type="ConsoleOutput">
    <outputString><![CDATA[	at org.junit.Assert.fail(Assert.java:88)
	at org.junit.Assert.assertTrue(Assert.java:41)
	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase.java:82)
	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase.java:167)
	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase.java:204)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
	at java.desktop/java.awt.Component.processMouseEvent(Component.java:6629)
	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)
	at java.desktop/java.awt.Component.processEvent(Component.java:6391)
	at java.desktop/java.awt.Container.processEvent(Container.java:2266)
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0"), Diff(INSERT,"	at org.junit.Assert.fail(Assert.java:88)
¶	at org.junit.Assert.assertTrue(Assert.java:41)
¶	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase.java:82)
¶	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase.java:167)
¶	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase.java:204)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
¶	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
¶	at java.desktop/java.awt.Component.processMouseEvent(Component.java:6629)
¶	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)
¶	at java.desktop/java.awt.Component.processEvent(Component.java:6391)
¶	at java.desktop/java.awt.Container.processEvent(Container.java:2266)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="101" _type="ConsoleOutput" date="Wed Jul 09 22:20:39 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="855186" type="ConsoleOutput">
    <outputString><![CDATA[Re-running test gradingTools.comp301ss21.assignment2.testcases.console_view.ConsoleSceneViewGetsBridgeScene@6e66cd2c . Results may change.
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"	at org.junit.Assert.fail(Assert.java:88)
¶	at org.junit.Assert.assertTrue(Assert.java:41)
¶	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase.java:82)
¶	a"), Diff(INSERT,"Re-running tes"), Diff(EQUAL,"t gradingTools.comp"), Diff(DELETE,"4"), Diff(INSERT,"3"), Diff(EQUAL,"01"), Diff(DELETE,"f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase.java:167)
¶	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase.java:204)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationM"), Diff(INSERT,"ss21.assignment2.testcases.console_view.ConsoleSceneViewGetsBridgeScene@6e66cd2c . Results may ch"), Diff(EQUAL,"an"), Diff(DELETE,"a"), Diff(EQUAL,"ge"), Diff(DELETE,"r.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
¶	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
¶	at java.desktop/java.awt.Component.processMouseEvent(Component.java:6629)
¶	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)
¶	at java.desktop/java.awt.Component.processEvent(Component.java:6391)
¶	at java.desktop/java.awt.Container.processEvent(Container.java:2266)"), Diff(INSERT,"."), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="102" _type="ShellCommand" date="Wed Jul 09 22:21:01 EDT 2025" starttimestamp="1752113184173" timestamp="877520" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="103" _type="ShellCommand" date="Wed Jul 09 22:21:39 EDT 2025" starttimestamp="1752113184173" timestamp="915817" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="104" _type="LocalChecksRawCommand" date="Wed Jul 09 22:21:40 EDT 2025" starttimestamp="1752113184173" timestamp="915904">
    <CSVRow><![CDATA[337,Wed Jul 09 22:20:39 EDT 2025,45,0,ConsoleSceneViewGetsBridgeScene,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,188,2,false, , , ,ConsoleSceneViewGetsBridgeScene-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="105" _type="ShellCommand" date="Wed Jul 09 22:21:49 EDT 2025" starttimestamp="1752113184173" timestamp="925653" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="106" _type="CopyCommand" date="Wed Jul 09 22:21:56 EDT 2025" starttimestamp="1752113184173" timestamp="932001" />
  <Command __id="107" _type="ShellCommand" date="Wed Jul 09 22:21:58 EDT 2025" starttimestamp="1752113184173" timestamp="933934" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="108" _type="ShellCommand" date="Wed Jul 09 22:22:53 EDT 2025" starttimestamp="1752113184173" timestamp="989263" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="109" _type="ShellCommand" date="Wed Jul 09 22:22:56 EDT 2025" starttimestamp="1752113184173" timestamp="991991" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="110" _type="ShellCommand" date="Wed Jul 09 22:23:06 EDT 2025" starttimestamp="1752113184173" timestamp="1001996" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="111" _type="ShellCommand" date="Wed Jul 09 22:23:11 EDT 2025" starttimestamp="1752113184173" timestamp="1006871" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="112" _type="ShellCommand" date="Wed Jul 09 22:23:23 EDT 2025" starttimestamp="1752113184173" timestamp="1018948" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="113" _type="ShellCommand" date="Wed Jul 09 22:23:26 EDT 2025" starttimestamp="1752113184173" timestamp="1022514" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="114" _type="ShellCommand" date="Wed Jul 09 22:25:10 EDT 2025" starttimestamp="1752113184173" timestamp="1126672" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="115" _type="ShellCommand" date="Wed Jul 09 22:25:14 EDT 2025" starttimestamp="1752113184173" timestamp="1130024" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="116" _type="ShellCommand" date="Wed Jul 09 22:26:39 EDT 2025" starttimestamp="1752113184173" timestamp="1215382" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="117" _type="ShellCommand" date="Wed Jul 09 22:26:40 EDT 2025" starttimestamp="1752113184173" timestamp="1216246" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="118" _type="ConsoleOutput" date="Wed Jul 09 22:26:46 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1222560" type="ConsoleOutput">
    <outputString><![CDATA[Re-running test gradingTools.comp301ss21.assignment2.testcases.console_view.ConsoleSceneViewGetsBridgeScene@6e66cd2c . Results may change.
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,"Re-running test gradingTools.comp301ss21.assignment2.testcases.console_view.ConsoleSceneViewGetsBridgeScene@6e66cd2c . Results may change.
¶")]]]></diff>
  </Command>
  <Command __id="120" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:26:55 EDT 2025" kind="HitBreakPoint" projectName="Assn2" starttimestamp="1752113184173" timestamp="1231119" type="Run" />
  <Command __id="121" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:26:55 EDT 2025" kind="Terminate" projectName="Assn2" starttimestamp="1752113184173" timestamp="1231120" type="Run" />
  <Command __id="122" _type="ShellCommand" date="Wed Jul 09 22:27:18 EDT 2025" starttimestamp="1752113184173" timestamp="1254545" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="123" _type="EclipseCommand" commandID="org.eclipse.ui.file.refresh" date="Wed Jul 09 22:27:23 EDT 2025" starttimestamp="1752113184173" timestamp="1259418" />
  <Command __id="124" _type="EclipseCommand" commandID="" date="Wed Jul 09 22:27:23 EDT 2025" starttimestamp="1752113184173" timestamp="1259638" />
  <Command __id="125" _type="EclipseCommand" commandID="" date="Wed Jul 09 22:27:23 EDT 2025" starttimestamp="1752113184173" timestamp="1259785" />
  <Command __id="126" _type="ShellCommand" date="Wed Jul 09 22:27:26 EDT 2025" starttimestamp="1752113184173" timestamp="1262275" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="132" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:27:38 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752113184173" timestamp="1274034" type="Run" />
  <Command __id="133" _type="RunCommand" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:27:38 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752113184173" timestamp="1274034" type="Run" />
  <Command __id="134" _type="LocalChecksRawCommand" date="Wed Jul 09 22:27:38 EDT 2025" starttimestamp="1752113184173" timestamp="1274062">
    <CSVRow><![CDATA[338,Wed Jul 09 22:23:19 EDT 2025,45,0,A2ConsoleSceneView,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,189,0,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,BridgeSceneSingletonFromFactory ConsoleSceneViewSingletonFromFactory TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="135" _type="LocalChecksRawCommand" date="Wed Jul 09 22:27:38 EDT 2025" starttimestamp="1752113184173" timestamp="1274062">
    <CSVRow><![CDATA[339,Wed Jul 09 22:25:32 EDT 2025,45,0,A2ConsoleSceneView,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,190,0,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,BridgeSceneSingletonFromFactory ConsoleSceneViewSingletonFromFactory TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="136" _type="LocalChecksRawCommand" date="Wed Jul 09 22:27:38 EDT 2025" starttimestamp="1752113184173" timestamp="1274062">
    <CSVRow><![CDATA[340,Wed Jul 09 22:25:38 EDT 2025,45,0,ConsoleSceneViewGetsBridgeScene,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,190,1,false, , , ,ConsoleSceneViewGetsBridgeScene-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="137" _type="LocalChecksRawCommand" date="Wed Jul 09 22:27:38 EDT 2025" starttimestamp="1752113184173" timestamp="1274062">
    <CSVRow><![CDATA[341,Wed Jul 09 22:25:41 EDT 2025,45,0,ConsoleSceneViewRegistersWithLocatables,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,190,2,false, , , ,ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="138" _type="LocalChecksRawCommand" date="Wed Jul 09 22:27:38 EDT 2025" starttimestamp="1752113184173" timestamp="1274062">
    <CSVRow><![CDATA[342,Wed Jul 09 22:25:43 EDT 2025,45,0,ConsoleSceneViewPrintsPropertyChangeEvent,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,190,3,false, , , ,ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="139" _type="LocalChecksRawCommand" date="Wed Jul 09 22:27:38 EDT 2025" starttimestamp="1752113184173" timestamp="1274062">
    <CSVRow><![CDATA[343,Wed Jul 09 22:25:45 EDT 2025,45,0,ConsoleSceneView,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,190,4,false, , , ,ConsoleSceneView-(0.0/50.0) , ,]]></CSVRow>
  </Command>
  <Command __id="140" _type="LocalChecksRawCommand" date="Wed Jul 09 22:27:38 EDT 2025" starttimestamp="1752113184173" timestamp="1274062">
    <CSVRow><![CDATA[344,Wed Jul 09 22:26:46 EDT 2025,45,0,ConsoleSceneViewGetsBridgeScene,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,188,3,false, , , ,ConsoleSceneViewGetsBridgeScene-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="141" _type="LocalChecksRawCommand" date="Wed Jul 09 22:27:38 EDT 2025" starttimestamp="1752113184173" timestamp="1274062">
    <CSVRow><![CDATA[345,Wed Jul 09 22:26:52 EDT 2025,45,0,A2ConsoleSceneView,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,188,4,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView , , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="142" _type="ShellCommand" date="Wed Jul 09 22:27:42 EDT 2025" starttimestamp="1752113184173" timestamp="1277870" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="143" _type="ConsoleOutput" date="Wed Jul 09 22:27:47 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1283613" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Factory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Running suite A2"), Diff(DELETE,"ConsoleSceneView"), Diff(INSERT,"Factory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="144" _type="ConsoleOutput" date="Wed Jul 09 22:27:47 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1283658" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:27:47 EDT 2025<<
>>Running test TaggedFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2"), Diff(INSERT,"Wed Jul 09 22:27:47 EDT 2025<<
¶>>Running test Tagged"), Diff(EQUAL,"Factory
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="145" _type="ConsoleOutput" date="Wed Jul 09 22:27:47 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1283674" type="ConsoleOutput">
    <outputString><![CDATA[>>TaggedFactory test execution time (ms):38<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:27:47 EDT 2025<<
¶>>Running test TaggedFactory
¶"), Diff(INSERT,"TaggedFactory test execution time (ms):38"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="146" _type="ConsoleOutput" date="Wed Jul 09 22:27:47 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1283678" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>T"), Diff(DELETE,"aggedFactory test execution time (ms):38"), Diff(INSERT,"est Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="147" _type="ConsoleOutput" date="Wed Jul 09 22:27:47 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1283681" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:27:47 EDT 2025<<
>>Running test BridgeSceneFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶TaggedFactory,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:27:47 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="148" _type="ConsoleOutput" date="Wed Jul 09 22:27:47 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1283685" type="ConsoleOutput">
    <outputString><![CDATA[>>Running checkstyle, this will take time<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:27:47 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined
¶"), Diff(INSERT,"Running checkstyle, this will take time"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="149" _type="ConsoleOutput" date="Wed Jul 09 22:27:47 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1283727" type="ConsoleOutput">
    <outputString><![CDATA[WARNING: A terminally deprecated method in java.lang.System has been called
WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
WARNING: System::setSecurityManager will be removed in a future release
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Running checkstyle, this will take time<<"), Diff(INSERT,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="150" _type="ConsoleOutput" date="Wed Jul 09 22:27:51 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1287787" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneFactoryMethodDefined test execution time (ms):4106<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(INSERT,">>BridgeSceneFactoryMethodDefined test execution time (ms):4106<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="151" _type="ConsoleOutput" date="Wed Jul 09 22:27:51 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1287801" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneFactoryMethodDefined"), Diff(DELETE," test execution time (ms):4106"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="152" _type="ConsoleOutput" date="Wed Jul 09 22:27:51 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1287806" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:27:51 EDT 2025<<
>>Running test BridgeSceneSingletonFromFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 22:27:51 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"FactoryMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"SingletonFromFactory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="153" _type="ConsoleOutput" date="Wed Jul 09 22:27:51 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1287824" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneSingletonFromFactory test execution time (ms):15<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:27:51 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeSceneSingletonFromFactory"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):15"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="154" _type="ConsoleOutput" date="Wed Jul 09 22:27:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1287828" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneSingletonFromFactory"), Diff(DELETE," test execution time (ms):15"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="155" _type="ConsoleOutput" date="Wed Jul 09 22:27:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1287831" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:27:52 EDT 2025<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:27:52 EDT 2025<<
¶>>Running test ConsoleSceneViewFactoryMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="156" _type="ConsoleOutput" date="Wed Jul 09 22:27:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1287899" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewFactoryMethodDefined test execution time (ms):68<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:27:52 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"ConsoleSceneViewFactoryMethodDefined"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):68"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="157" _type="ConsoleOutput" date="Wed Jul 09 22:27:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1287903" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"ConsoleSceneViewFactoryMethodDefined"), Diff(DELETE," test execution time (ms):68"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="158" _type="ConsoleOutput" date="Wed Jul 09 22:27:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1287907" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:27:52 EDT 2025<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 22:27:52 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"ConsoleSceneView"), Diff(DELETE,"FactoryMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"SingletonFromFactory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="159" _type="ConsoleOutput" date="Wed Jul 09 22:27:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1287910" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewSingletonFromFactory test execution time (ms):3<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:27:52 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"ConsoleSceneViewSingletonFromFactory"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):3"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="160" _type="ConsoleOutput" date="Wed Jul 09 22:27:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1287916" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"ConsoleSceneViewSingletonFromFactory"), Diff(DELETE," test execution time (ms):3"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="161" _type="ConsoleOutput" date="Wed Jul 09 22:27:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1287918" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:27:52 EDT 2025<<
>>Running test LegsFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:27:52 EDT 2025<<
¶>>Running test LegsFactoryMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="162" _type="ConsoleOutput" date="Wed Jul 09 22:27:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1287981" type="ConsoleOutput">
    <outputString><![CDATA[>>LegsFactoryMethodDefined test execution time (ms):65<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:27:52 EDT 2025<<
¶>>Running test LegsFactoryMethodDefined
¶"), Diff(INSERT,"LegsFactoryMethodDefined test execution time (ms):65"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="163" _type="ConsoleOutput" date="Wed Jul 09 22:27:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1287990" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:27:52 EDT 2025<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"LegsFactoryMethodDefined"), Diff(DELETE," test execution time (ms):65"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:27:52 EDT 2025<<
¶>>Running test A2MainCallsBridgeSceneFactoryMethod
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="164" _type="ConsoleOutput" date="Wed Jul 09 22:27:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1288055" type="ConsoleOutput">
    <outputString><![CDATA[>>A2MainCallsBridgeSceneFactoryMethod test execution time (ms):68<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:27:52 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):68"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="165" _type="ConsoleOutput" date="Wed Jul 09 22:27:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1288060" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE," test execution time (ms):68"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="166" _type="ConsoleOutput" date="Wed Jul 09 22:27:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1288062" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:27:52 EDT 2025<<
>>Running test AvatarCallsLegFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:27:52 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="167" _type="ConsoleOutput" date="Wed Jul 09 22:27:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1288087" type="ConsoleOutput">
    <outputString><![CDATA[>>AvatarCallsLegFactoryMethod test execution time (ms):26<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:27:52 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod
¶"), Diff(INSERT,"AvatarCallsLegFactoryMethod test execution time (ms):26"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="168" _type="ConsoleOutput" date="Wed Jul 09 22:27:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1288091" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"AvatarCallsLegFactoryMethod"), Diff(DELETE," test execution time (ms):26"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="169" _type="ConsoleOutput" date="Wed Jul 09 22:27:54 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1290063" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2ConsoleSceneView
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,"), Diff(INSERT,"Running suite A2ConsoleSceneView"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="170" _type="ConsoleOutput" date="Wed Jul 09 22:27:54 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1290073" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:27:54 EDT 2025<<
>>Running test TaggedConsoleSceneView
<<
>>TaggedConsoleSceneView test execution time (ms):2<<
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Wed Jul 09 22:27:54 EDT 2025<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Wed Jul 09 22:27:54 EDT 2025<<
>>Running test TaggedLocatable
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2ConsoleSceneView"), Diff(INSERT,"Wed Jul 09 22:27:54 EDT 2025<<
¶>>Running test TaggedConsoleSceneView
¶<<
¶>>TaggedConsoleSceneView test execution time (ms):2<<
¶>>Test Result:
¶TaggedConsoleSceneView,100.0% complete,0.0,0.0,
¶<<
¶>>Wed Jul 09 22:27:54 EDT 2025<<
¶>>Running test ConsoleSceneViewGetsBridgeScene
¶<<
¶>>Wed Jul 09 22:27:54 EDT 2025<<
¶>>Running test TaggedLocatable"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="171" _type="ConsoleOutput" date="Wed Jul 09 22:27:54 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1290076" type="ConsoleOutput">
    <outputString><![CDATA[>>Steps traced since last test:
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:27:54 EDT 2025<<
¶>>Running test TaggedConsoleSceneView
¶<<
¶>>TaggedConsoleSceneView test execution time (ms):2<<
¶>>Test Result:
¶TaggedConsoleSceneView,100.0% complete,0.0,0.0,
¶<<
¶>>Wed Jul 09 22:27:54 EDT 2025<<
¶>>Running test ConsoleSceneViewGetsBridgeScene
¶<<
¶>>Wed Jul 09 22:27:54 EDT 2025<<
¶>>Running test TaggedLocatable
¶<<"), Diff(INSERT,"Steps traced since last test:"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="172" _type="ConsoleOutput" date="Wed Jul 09 22:27:54 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1290078" type="ConsoleOutput">
    <outputString><![CDATA[>>TaggedLocatable test execution time (ms):5<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Steps traced since last test:"), Diff(INSERT,"TaggedLocatable test execution time (ms):5<<
¶>>Test Result:
¶TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="173" _type="ConsoleOutput" date="Wed Jul 09 22:27:54 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1290081" type="ConsoleOutput">
    <outputString><![CDATA[>>Steps traced since last test:

>>ConsoleSceneViewGetsBridgeScene test execution time (ms):6<<
>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"TaggedLocatabl"), Diff(INSERT,"Steps traced since last test:
¶
¶>>ConsoleSceneViewGetsBridgeScen"), Diff(EQUAL,"e test execution time (ms):"), Diff(DELETE,"5"), Diff(INSERT,"6"), Diff(EQUAL,"<<
¶>>Test Result:
¶"), Diff(DELETE,"TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable"), Diff(INSERT,"ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="174" _type="ConsoleOutput" date="Wed Jul 09 22:27:54 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1290085" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:27:54 EDT 2025<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>ConsoleSceneViewRegistersWithLocatables test execution time (ms):0<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:27:54 EDT 2025<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Steps traced since last test:
¶
¶>>ConsoleSceneViewGetsBridgeScene"), Diff(INSERT,"Wed Jul 09 22:27:54 EDT 2025<<
¶>>Running test ConsoleSceneViewRegistersWithLocatables
¶<<
¶>>ConsoleSceneViewRegistersWithLocatables"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"6"), Diff(INSERT,"0"), Diff(EQUAL,"<<
¶>>Test Result:
¶ConsoleSceneView"), Diff(DELETE,"GetsBridgeScene"), Diff(INSERT,"RegistersWithLocatables"), Diff(EQUAL,",0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶"), Diff(INSERT,">>Wed Jul 09 22:27:54 EDT 2025<<
¶>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="175" _type="ConsoleOutput" date="Wed Jul 09 22:27:54 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1290088" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):0<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:27:54 EDT 2025<<
¶>>Running test ConsoleSceneViewRegistersWithLocatables
¶<<
¶>>ConsoleSceneViewRegistersWithLocatables"), Diff(INSERT,"ConsoleSceneViewPrintsPropertyChangeEvent"), Diff(EQUAL," test execution time (ms):0<<
¶>>Test Result:
¶ConsoleSceneView"), Diff(DELETE,"RegistersWithLocatables"), Diff(INSERT,"PrintsPropertyChangeEvent"), Diff(EQUAL,",0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶"), Diff(DELETE,">>Wed Jul 09 22:27:54 EDT 2025<<
¶>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="176" _type="ConsoleOutput" date="Wed Jul 09 22:27:54 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1290091" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:27:54 EDT 2025<<
>>Running test ConsoleSceneView
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):0<<
¶>>Test Result:
¶ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test"), Diff(INSERT,"Wed Jul 09 22:27:54 EDT 2025<<
¶>>Running test ConsoleSceneView"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="177" _type="LocalChecksRawCommand" date="Wed Jul 09 22:27:54 EDT 2025" starttimestamp="1752113184173" timestamp="1290105">
    <CSVRow><![CDATA[346,Wed Jul 09 22:27:52 EDT 2025,45,0,A2Factory,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,191,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(2.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(2.0/2.0) TaggedFactory-(2.0/2.0) , ,]]></CSVRow>
  </Command>
  <Command __id="178" _type="ExceptionCommand" date="Wed Jul 09 22:27:54 EDT 2025" starttimestamp="1752113184173" timestamp="1290115" type="Exception">
    <exceptionString><![CDATA[java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0
	at org.junit.Assert.fail(Assert.java:88)
	at org.junit.Assert.assertTrue(Assert.java:41)
	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase.java:82)
	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase.java:167)
	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase.java:204)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
]]></exceptionString>
    <language><![CDATA[SML]]></language>
  </Command>
  <Command __id="179" _type="ConsoleOutput" date="Wed Jul 09 22:27:54 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1290117" type="ConsoleOutput">
    <outputString><![CDATA[java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0
	at org.junit.Assert.fail(Assert.java:88)
	at org.junit.Assert.assertTrue(Assert.java:41)
	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase.java:82)
	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase.java:167)
	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase.java:204)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Wed Jul 09 22:27:54 EDT 2025<<
¶>>Running test ConsoleSceneView
¶<<"), Diff(INSERT,"java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0
¶	at org.junit.Assert.fail(Assert.java:88)
¶	at org.junit.Assert.assertTrue(Assert.java:41)
¶	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase.java:82)
¶	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase.java:167)
¶	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase.java:204)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="180" _type="ConsoleOutput" date="Wed Jul 09 22:27:54 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1290165" type="ConsoleOutput">
    <outputString><![CDATA[	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
	at java.desktop/java.awt.Component.processMouseEvent(Component.java:6629)
	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)
	at java.desktop/java.awt.Component.processEvent(Component.java:6391)
	at java.desktop/java.awt.Container.processEvent(Container.java:2266)
	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
	at java.desktop/java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948)
	at java.desktop/java.awt.LightweightDispatcher.processMouseEvent(Container.java:4584)
	at java.desktop/java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516)
	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2310)
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,"	at "), Diff(EQUAL,"java."), Diff(DELETE,"lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0
¶	at org.junit.Assert.fail(Assert"), Diff(INSERT,"base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner"), Diff(EQUAL,".java:"), Diff(INSERT,"2"), Diff(EQUAL,"88)
¶	at org.junit."), Diff(DELETE,"Assert.assertTrue(Assert"), Diff(INSERT,"runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner"), Diff(EQUAL,".java:"), Diff(DELETE,"41"), Diff(INSERT,"363"), Diff(EQUAL,")
¶	at grad"), Diff(DELETE,"ingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase"), Diff(INSERT,"er.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method"), Diff(EQUAL,".java:"), Diff(INSERT,"56"), Diff(EQUAL,"8"), Diff(DELETE,"2"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase"), Diff(INSERT,"bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager"), Diff(EQUAL,".java:1"), Diff(DELETE,"67"), Diff(INSERT,"538"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase"), Diff(INSERT,"bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
¶	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
¶	at java.desktop/java.awt.Component.processMouseEvent(Component"), Diff(EQUAL,".java:"), Diff(DELETE,"204"), Diff(INSERT,"6629"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTes"), Diff(INSERT,"java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)
¶	at java.desktop/java.awt.Component.processEvent(Component.java:6391)
¶	at java.desktop/java.awt.Container.processEvent(Container.java:2266)
¶	at java.desktop/java.awt.Component.dispatchEventImpl(Componen"), Diff(EQUAL,"t.java:"), Diff(DELETE,"77"), Diff(INSERT,"500"), Diff(EQUAL,"1)
¶	at "), Diff(DELETE,"gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest"), Diff(INSERT,"java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
¶	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
¶	at java.desktop/java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948)
¶	at java.desktop/java.awt.LightweightDispatcher.processMouseEvent(Container.java:4584)
¶	at java.desktop/java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516)
¶	at java.desktop/java.awt.Container.dispatchEventImpl(Container"), Diff(EQUAL,".java:"), Diff(DELETE,"764"), Diff(INSERT,"2310"), Diff(EQUAL,")
¶")]]]></diff>
  </Command>
  <Command __id="181" _type="ConsoleOutput" date="Wed Jul 09 22:28:08 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1304338" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite BridgeSceneSemantics
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
¶	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
¶	at java.desktop/java.awt.Component.processMouseEvent(Component.java:6629)
¶	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)
¶	at java.desktop/java.awt.Component.processEvent(Component.java:6391)
¶	at java.desktop/java.awt.Container.processEvent(Container.java:2266)
¶	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
¶	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
¶	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
¶	at java.desktop/java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948)
¶	at java.desktop/java.awt.LightweightDispatcher.processMouseEvent(Container.java:4584)
¶	at java.desktop/java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516)
¶	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2310)"), Diff(INSERT,">>Running suite BridgeSceneSemantics
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="182" _type="ConsoleOutput" date="Wed Jul 09 22:28:08 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1304344" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:28:08 EDT 2025<<
>>Running test BridgeSceneApproachMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,">>Wed Jul 09 22:28:08 EDT 2025<<
¶"), Diff(EQUAL,">>Running "), Diff(DELETE,"sui"), Diff(EQUAL,"te"), Diff(INSERT,"st"), Diff(EQUAL," BridgeScene"), Diff(DELETE,"Semantics"), Diff(INSERT,"ApproachMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="183" _type="ConsoleOutput" date="Wed Jul 09 22:28:08 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1304410" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneApproachMethodDefined test execution time (ms):73<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:28:08 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeSceneApproachMethodDefined"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):73"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="184" _type="ConsoleOutput" date="Wed Jul 09 22:28:08 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1304420" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:28:08 EDT 2025<<
>>Running test BridgeSceneSayMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneApproachMethodDefined"), Diff(DELETE," test execution time (ms):73"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:28:08 EDT 2025<<
¶>>Running test BridgeSceneSayMethodDefined
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="185" _type="ConsoleOutput" date="Wed Jul 09 22:28:08 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1304446" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneSayMethodDefined test execution time (ms):27<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"Approach"), Diff(INSERT,"Say"), Diff(EQUAL,"MethodDefined"), Diff(DELETE,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:28:08 EDT 2025<<
¶>>Running test BridgeSceneSayMethodDefined
¶"), Diff(INSERT," test execution time (ms):27"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="186" _type="ConsoleOutput" date="Wed Jul 09 22:28:08 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1304454" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:28:08 EDT 2025<<
>>Running test BridgeScenePassedMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneSayMethodDefined"), Diff(DELETE," test execution time (ms):27"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:28:08 EDT 2025<<
¶>>Running test BridgeScenePassedMethodDefined
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="187" _type="ConsoleOutput" date="Wed Jul 09 22:28:08 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1304471" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeScenePassedMethodDefined test execution time (ms):21<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"Say"), Diff(INSERT,"Passed"), Diff(EQUAL,"MethodDefined"), Diff(DELETE,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:28:08 EDT 2025<<
¶>>Running test BridgeScenePassedMethodDefined
¶"), Diff(INSERT," test execution time (ms):21"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="188" _type="ConsoleOutput" date="Wed Jul 09 22:28:08 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1304482" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:28:08 EDT 2025<<
>>Running test BridgeSceneFailedMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeScenePassedMethodDefined"), Diff(DELETE," test execution time (ms):21"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:28:08 EDT 2025<<
¶>>Running test BridgeSceneFailedMethodDefined
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="189" _type="ConsoleOutput" date="Wed Jul 09 22:28:08 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1304493" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneFailedMethodDefined test execution time (ms):17<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"Pass"), Diff(INSERT,"Fail"), Diff(EQUAL,"edMethodDefined"), Diff(DELETE,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:28:08 EDT 2025<<
¶>>Running test BridgeSceneFailedMethodDefined
¶"), Diff(INSERT," test execution time (ms):17"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="190" _type="ConsoleOutput" date="Wed Jul 09 22:28:08 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1304498" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneFailedMethodDefined"), Diff(DELETE," test execution time (ms):17"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="191" _type="ConsoleOutput" date="Wed Jul 09 22:28:08 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1304500" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:28:08 EDT 2025<<
>>Running test BridgeSceneDynamics
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:28:08 EDT 2025<<
¶>>Running test BridgeSceneDynamics"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="192" _type="ConsoleOutput" date="Wed Jul 09 22:28:08 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1304510" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneDynamics test execution time (ms):11<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:28:08 EDT 2025<<
¶>>Running test BridgeSceneDynamics
¶"), Diff(INSERT,"BridgeSceneDynamics test execution time (ms):11"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="193" _type="ConsoleOutput" date="Wed Jul 09 22:28:08 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1304517" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneDynamics"), Diff(DELETE," test execution time (ms):11"), Diff(INSERT,",100.0% complete,50.0,50.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="194" _type="ConsoleOutput" date="Wed Jul 09 22:28:10 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="1306182" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite BridgeSceneSemantics
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶BridgeSceneDynamics,100.0% complete,50.0,50.0,"), Diff(INSERT,"Running suite BridgeSceneSemantics"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="195" _type="LocalChecksRawCommand" date="Wed Jul 09 22:28:10 EDT 2025" starttimestamp="1752113184173" timestamp="1306211">
    <CSVRow><![CDATA[348,Wed Jul 09 22:28:08 EDT 2025,45,0,BridgeSceneSemantics,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,191,2,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(50.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,]]></CSVRow>
  </Command>
  <Command __id="196" _type="LocalChecksRawCommand" date="Wed Jul 09 22:28:10 EDT 2025" starttimestamp="1752113184173" timestamp="1306211">
    <CSVRow><![CDATA[349,Wed Jul 09 22:28:10 EDT 2025,45,0,BridgeSceneSemantics,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,191,3,true,BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined , , ,BridgeSceneApproachMethodDefined-(2.0/2.0) BridgeSceneDynamics-(50.0/50.0) BridgeSceneFailedMethodDefined-(2.0/2.0) BridgeScenePassedMethodDefined-(2.0/2.0) BridgeSceneSayMethodDefined-(2.0/2.0) , ,]]></CSVRow>
  </Command>
  <Command __id="197" _type="ShellCommand" date="Wed Jul 09 22:28:25 EDT 2025" starttimestamp="1752113184173" timestamp="1321455" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="198" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:28:25 EDT 2025" kind="HitBreakPoint" projectName="Assn2" starttimestamp="1752113184173" timestamp="1321642" type="Run" />
  <Command __id="199" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:28:25 EDT 2025" kind="Terminate" projectName="Assn2" starttimestamp="1752113184173" timestamp="1321642" type="Run" />
  <Command __id="200" _type="ShellCommand" date="Wed Jul 09 22:28:27 EDT 2025" starttimestamp="1752113184173" timestamp="1323495" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="201" _type="ShellCommand" date="Wed Jul 09 22:28:40 EDT 2025" starttimestamp="1752113184173" timestamp="1336681" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="202" _type="ShellCommand" date="Wed Jul 09 22:28:41 EDT 2025" starttimestamp="1752113184173" timestamp="1337482" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="203" _type="ShellCommand" date="Wed Jul 09 22:29:12 EDT 2025" starttimestamp="1752113184173" timestamp="1368631" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="204" _type="ShellCommand" date="Wed Jul 09 22:29:15 EDT 2025" starttimestamp="1752113184173" timestamp="1371553" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="205" _type="ShellCommand" date="Wed Jul 09 22:45:13 EDT 2025" starttimestamp="1752113184173" timestamp="2329751" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="206" _type="EclipseCommand" commandID="org.eclipse.ui.file.refresh" date="Wed Jul 09 22:45:18 EDT 2025" starttimestamp="1752113184173" timestamp="2334095" />
  <Command __id="207" _type="EclipseCommand" commandID="" date="Wed Jul 09 22:45:18 EDT 2025" starttimestamp="1752113184173" timestamp="2334304" />
  <Command __id="208" _type="EclipseCommand" commandID="" date="Wed Jul 09 22:45:18 EDT 2025" starttimestamp="1752113184173" timestamp="2334452" />
  <Command __id="209" _type="ShellCommand" date="Wed Jul 09 22:45:22 EDT 2025" starttimestamp="1752113184173" timestamp="2338694" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="215" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:45:29 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752113184173" timestamp="2345334" type="Run" />
  <Command __id="216" _type="RunCommand" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:45:29 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752113184173" timestamp="2345334" type="Run" />
  <Command __id="217" _type="LocalChecksRawCommand" date="Wed Jul 09 22:45:29 EDT 2025" starttimestamp="1752113184173" timestamp="2345362">
    <CSVRow><![CDATA[350,Wed Jul 09 22:33:52 EDT 2025,45,0,A2ConsoleSceneView,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,192,0,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,BridgeSceneSingletonFromFactory ConsoleSceneViewSingletonFromFactory TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="218" _type="LocalChecksRawCommand" date="Wed Jul 09 22:45:29 EDT 2025" starttimestamp="1752113184173" timestamp="2345362">
    <CSVRow><![CDATA[351,Wed Jul 09 22:33:57 EDT 2025,45,0,ConsoleSceneViewGetsBridgeScene,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,192,1,false, , , ,ConsoleSceneViewGetsBridgeScene-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="219" _type="LocalChecksRawCommand" date="Wed Jul 09 22:45:29 EDT 2025" starttimestamp="1752113184173" timestamp="2345362">
    <CSVRow><![CDATA[352,Wed Jul 09 22:34:01 EDT 2025,45,0,ConsoleSceneViewRegistersWithLocatables,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,192,2,false, , , ,ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="220" _type="LocalChecksRawCommand" date="Wed Jul 09 22:45:29 EDT 2025" starttimestamp="1752113184173" timestamp="2345362">
    <CSVRow><![CDATA[353,Wed Jul 09 22:34:02 EDT 2025,45,0,ConsoleSceneViewRegistersWithLocatables,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,192,3,false, , , ,ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="221" _type="LocalChecksRawCommand" date="Wed Jul 09 22:45:29 EDT 2025" starttimestamp="1752113184173" timestamp="2345362">
    <CSVRow><![CDATA[354,Wed Jul 09 22:34:03 EDT 2025,45,0,ConsoleSceneViewRegistersWithLocatables,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,192,4,false, , , ,ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="222" _type="LocalChecksRawCommand" date="Wed Jul 09 22:45:29 EDT 2025" starttimestamp="1752113184173" timestamp="2345362">
    <CSVRow><![CDATA[355,Wed Jul 09 22:35:22 EDT 2025,45,0,A2ConsoleSceneView,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,193,0,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,BridgeSceneSingletonFromFactory ConsoleSceneViewSingletonFromFactory TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="223" _type="LocalChecksRawCommand" date="Wed Jul 09 22:45:29 EDT 2025" starttimestamp="1752113184173" timestamp="2345363">
    <CSVRow><![CDATA[356,Wed Jul 09 22:38:58 EDT 2025,45,0,A2ConsoleSceneView,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,194,0,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,BridgeSceneSingletonFromFactory ConsoleSceneViewSingletonFromFactory TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="224" _type="ShellCommand" date="Wed Jul 09 22:45:34 EDT 2025" starttimestamp="1752113184173" timestamp="2350329" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="225" _type="ConsoleOutput" date="Wed Jul 09 22:45:40 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2356021" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Factory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Running suite "), Diff(DELETE,"BridgeSceneSemantics"), Diff(INSERT,"A2Factory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="226" _type="ConsoleOutput" date="Wed Jul 09 22:45:40 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2356068" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:45:40 EDT 2025<<
>>Running test TaggedFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2"), Diff(INSERT,"Wed Jul 09 22:45:40 EDT 2025<<
¶>>Running test Tagged"), Diff(EQUAL,"Factory
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="227" _type="ConsoleOutput" date="Wed Jul 09 22:45:40 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2356088" type="ConsoleOutput">
    <outputString><![CDATA[>>TaggedFactory test execution time (ms):41<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:45:40 EDT 2025<<
¶>>Running test TaggedFactory
¶"), Diff(INSERT,"TaggedFactory test execution time (ms):41"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="228" _type="ConsoleOutput" date="Wed Jul 09 22:45:40 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2356102" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>T"), Diff(DELETE,"aggedFactory test execution time (ms):41"), Diff(INSERT,"est Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="229" _type="ConsoleOutput" date="Wed Jul 09 22:45:40 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2356107" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:45:40 EDT 2025<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running checkstyle, this will take time<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶"), Diff(INSERT,"Wed Jul 09 22:45:40 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined
¶<<
¶>>Running checkstyle, this will take time"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="230" _type="ConsoleOutput" date="Wed Jul 09 22:45:40 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2356152" type="ConsoleOutput">
    <outputString><![CDATA[WARNING: A terminally deprecated method in java.lang.System has been called
WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
WARNING: System::setSecurityManager will be removed in a future release
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Wed Jul 09 22:45:40 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined
¶<<
¶>>Running checkstyle, this will take time<<"), Diff(INSERT,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="231" _type="ConsoleOutput" date="Wed Jul 09 22:45:45 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361684" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneFactoryMethodDefined test execution time (ms):5581<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(INSERT,">>BridgeSceneFactoryMethodDefined test execution time (ms):5581<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="232" _type="ConsoleOutput" date="Wed Jul 09 22:45:45 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361694" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneFactoryMethodDefined"), Diff(DELETE," test execution time (ms):5581"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="233" _type="ConsoleOutput" date="Wed Jul 09 22:45:45 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361705" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:45:45 EDT 2025<<
>>Running test BridgeSceneSingletonFromFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 22:45:45 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"FactoryMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"SingletonFromFactory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="234" _type="ConsoleOutput" date="Wed Jul 09 22:45:45 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361725" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneSingletonFromFactory test execution time (ms):20<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:45:45 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeSceneSingletonFromFactory"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):20"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="235" _type="ConsoleOutput" date="Wed Jul 09 22:45:45 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361730" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneSingletonFromFactory"), Diff(DELETE," test execution time (ms):20"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="236" _type="ConsoleOutput" date="Wed Jul 09 22:45:45 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361741" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:45:45 EDT 2025<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:45:45 EDT 2025<<
¶>>Running test ConsoleSceneViewFactoryMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="237" _type="ConsoleOutput" date="Wed Jul 09 22:45:45 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361824" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewFactoryMethodDefined test execution time (ms):94<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:45:45 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"ConsoleSceneViewFactoryMethodDefined"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):94"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="238" _type="ConsoleOutput" date="Wed Jul 09 22:45:46 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361831" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"ConsoleSceneViewFactoryMethodDefined"), Diff(DELETE," test execution time (ms):94"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="239" _type="ConsoleOutput" date="Wed Jul 09 22:45:46 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361838" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:45:46 EDT 2025<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 22:45:46 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"ConsoleSceneView"), Diff(DELETE,"FactoryMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"SingletonFromFactory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="240" _type="ConsoleOutput" date="Wed Jul 09 22:45:46 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361842" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewSingletonFromFactory test execution time (ms):4<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:45:46 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"ConsoleSceneViewSingletonFromFactory"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):4"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="241" _type="ConsoleOutput" date="Wed Jul 09 22:45:46 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361847" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"ConsoleSceneViewSingletonFromFactory"), Diff(DELETE," test execution time (ms):4"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="242" _type="ConsoleOutput" date="Wed Jul 09 22:45:46 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361852" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:45:46 EDT 2025<<
>>Running test LegsFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:45:46 EDT 2025<<
¶>>Running test LegsFactoryMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="243" _type="ConsoleOutput" date="Wed Jul 09 22:45:46 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361889" type="ConsoleOutput">
    <outputString><![CDATA[>>LegsFactoryMethodDefined test execution time (ms):41<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:45:46 EDT 2025<<
¶>>Running test LegsFactoryMethodDefined
¶"), Diff(INSERT,"LegsFactoryMethodDefined test execution time (ms):41"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="244" _type="ConsoleOutput" date="Wed Jul 09 22:45:46 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361896" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"LegsFactoryMethodDefined"), Diff(DELETE," test execution time (ms):41"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="245" _type="ConsoleOutput" date="Wed Jul 09 22:45:46 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361902" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:45:46 EDT 2025<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶LegsFactoryMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:45:46 EDT 2025<<
¶>>Running test A2MainCallsBridgeSceneFactoryMethod"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="246" _type="ConsoleOutput" date="Wed Jul 09 22:45:46 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361937" type="ConsoleOutput">
    <outputString><![CDATA[>>A2MainCallsBridgeSceneFactoryMethod test execution time (ms):42<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:45:46 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):42"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="247" _type="ConsoleOutput" date="Wed Jul 09 22:45:46 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361943" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE," test execution time (ms):42"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="248" _type="ConsoleOutput" date="Wed Jul 09 22:45:46 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361957" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:45:46 EDT 2025<<
>>Running test AvatarCallsLegFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 22:45:46 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="249" _type="ConsoleOutput" date="Wed Jul 09 22:45:46 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361981" type="ConsoleOutput">
    <outputString><![CDATA[>>AvatarCallsLegFactoryMethod test execution time (ms):37<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:45:46 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod
¶"), Diff(INSERT,"AvatarCallsLegFactoryMethod test execution time (ms):37"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="250" _type="ConsoleOutput" date="Wed Jul 09 22:45:46 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2361986" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"AvatarCallsLegFactoryMethod"), Diff(DELETE," test execution time (ms):37"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="251" _type="ConsoleOutput" date="Wed Jul 09 22:45:49 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2365389" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Factory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,"), Diff(INSERT,"Running suite A2Factory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="252" _type="LocalChecksRawCommand" date="Wed Jul 09 22:45:49 EDT 2025" starttimestamp="1752113184173" timestamp="2365453">
    <CSVRow><![CDATA[357,Wed Jul 09 22:45:46 EDT 2025,45,0,A2Factory,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,195,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(2.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(2.0/2.0) TaggedFactory-(2.0/2.0) , ,]]></CSVRow>
  </Command>
  <Command __id="253" _type="LocalChecksRawCommand" date="Wed Jul 09 22:45:49 EDT 2025" starttimestamp="1752113184173" timestamp="2365453">
    <CSVRow><![CDATA[358,Wed Jul 09 22:45:49 EDT 2025,45,0,A2Factory,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,195,1,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(2.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(2.0/2.0) TaggedFactory-(2.0/2.0) , ,]]></CSVRow>
  </Command>
  <Command __id="254" _type="ConsoleOutput" date="Wed Jul 09 22:45:51 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2367341" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2ConsoleSceneView
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Running suite A2"), Diff(DELETE,"Factory"), Diff(INSERT,"ConsoleSceneView"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="255" _type="ConsoleOutput" date="Wed Jul 09 22:45:51 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2367348" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:45:51 EDT 2025<<
>>Running test TaggedConsoleSceneView
<<
>>TaggedConsoleSceneView test execution time (ms):3<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2ConsoleSceneView
¶"), Diff(INSERT,"Wed Jul 09 22:45:51 EDT 2025<<
¶>>Running test TaggedConsoleSceneView
¶<<
¶>>TaggedConsoleSceneView test execution time (ms):3"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="256" _type="ConsoleOutput" date="Wed Jul 09 22:45:51 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2367351" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
>>Wed Jul 09 22:45:51 EDT 2025<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,">>Test Result:
¶TaggedConsoleSceneView,100.0% complete,0.0,0.0,
¶<<
¶"), Diff(EQUAL,">>Wed Jul 09 22:45:51 EDT 2025<<
¶>>Running test "), Diff(DELETE,"Tagged"), Diff(EQUAL,"ConsoleSceneView"), Diff(DELETE,"
¶<<
¶>>TaggedConsoleSceneView test execution time (ms):3"), Diff(INSERT,"GetsBridgeScene
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="257" _type="ConsoleOutput" date="Wed Jul 09 22:45:51 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2367356" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:45:51 EDT 2025<<
>>Running test TaggedLocatable
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Test Result:
¶TaggedConsoleSceneView,100.0% complete,0.0,0.0,
¶<<
¶"), Diff(EQUAL,">>Wed Jul 09 22:45:51 EDT 2025<<
¶>>Running test "), Diff(DELETE,"ConsoleSceneViewGetsBridgeScen"), Diff(INSERT,"TaggedLocatabl"), Diff(EQUAL,"e
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="258" _type="ConsoleOutput" date="Wed Jul 09 22:45:51 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2367361" type="ConsoleOutput">
    <outputString><![CDATA[>>Steps traced since last test:

>>TaggedLocatable test execution time (ms):7<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
>>ConsoleSceneViewGetsBridgeScene test execution time (ms):9<<
>>Steps traced since last test:
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:45:51 EDT 2025<<
¶>>Running test TaggedLocatable
¶<<"), Diff(INSERT,"Steps traced since last test:
¶
¶>>TaggedLocatable test execution time (ms):7<<
¶>>Test Result:
¶TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
¶<<
¶>>ConsoleSceneViewGetsBridgeScene test execution time (ms):9<<
¶>>Steps traced since last test:"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="259" _type="ConsoleOutput" date="Wed Jul 09 22:45:51 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2367367" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:45:51 EDT 2025<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>ConsoleSceneViewRegistersWithLocatables test execution time (ms):1<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Steps traced since last"), Diff(INSERT,"Test Result:
¶ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this"), Diff(EQUAL," test"), Diff(DELETE,":"), Diff(EQUAL,"
¶"), Diff(INSERT,"<<"), Diff(EQUAL,"
¶>>"), Diff(DELETE,"Tagged"), Diff(INSERT,"Wed Jul 09 22:45:51 EDT 2025<<
¶>>Running test ConsoleSceneViewRegistersWithLocatables
¶<<
¶>>ConsoleSceneViewRegistersWith"), Diff(EQUAL,"Locatable"), Diff(INSERT,"s"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"7"), Diff(INSERT,"1"), Diff(EQUAL,"<<
¶>>Test Result:
¶"), Diff(DELETE,"Tagged"), Diff(INSERT,"ConsoleSceneViewRegistersWith"), Diff(EQUAL,"Locatable"), Diff(INSERT,"s"), Diff(EQUAL,",0.0% complete,0.0,0.0,"), Diff(DELETE,"No class in project matching name/tag:Locatable
¶<<
¶>>ConsoleSceneViewGetsBridgeScene test execution time (ms):9<<
¶>>Steps traced since last"), Diff(INSERT,"
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this"), Diff(EQUAL," test"), Diff(DELETE,":"), Diff(INSERT,"
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="260" _type="ConsoleOutput" date="Wed Jul 09 22:45:51 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2367371" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:45:51 EDT 2025<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):1<<
>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:45:51 EDT 2025<<
>>Running test ConsoleSceneView
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Test Result:
¶ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶"), Diff(EQUAL,">>Wed Jul 09 22:45:51 EDT 2025<<
¶>>Running test ConsoleSceneView"), Diff(DELETE,"RegistersWithLocatables
¶<<
¶>>ConsoleSceneViewRegistersWithLocatables"), Diff(INSERT,"PrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEvent"), Diff(EQUAL," test execution time (ms):1<<
¶>>Test Result:
¶ConsoleSceneView"), Diff(DELETE,"RegistersWithLocatables"), Diff(INSERT,"PrintsPropertyChangeEvent"), Diff(EQUAL,",0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶"), Diff(INSERT,">>Wed Jul 09 22:45:51 EDT 2025<<
¶>>Running test ConsoleSceneView
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="261" _type="ExceptionCommand" date="Wed Jul 09 22:45:51 EDT 2025" starttimestamp="1752113184173" timestamp="2367400" type="Exception">
    <exceptionString><![CDATA[java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0
	at org.junit.Assert.fail(Assert.java:88)
	at org.junit.Assert.assertTrue(Assert.java:41)
	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase.java:82)
	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase.java:167)
	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase.java:204)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
	at java.desktop/java.awt.Component.processMouseEvent(Component.java:6629)
	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)
]]></exceptionString>
    <language><![CDATA[SML]]></language>
  </Command>
  <Command __id="262" _type="ConsoleOutput" date="Wed Jul 09 22:45:51 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2367474" type="ConsoleOutput">
    <outputString><![CDATA[java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0
	at org.junit.Assert.fail(Assert.java:88)
	at org.junit.Assert.assertTrue(Assert.java:41)
	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase.java:82)
	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase.java:167)
	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase.java:204)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
	at java.desktop/java.awt.Component.processMouseEvent(Component.java:6629)
	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Wed Jul 09 22:45:51 EDT 2025<<
¶>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):1<<
¶>>Test Result:
¶ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:45:51 EDT 2025<<
¶>>Running test ConsoleSceneView
¶<<"), Diff(INSERT,"java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0
¶	at org.junit.Assert.fail(Assert.java:88)
¶	at org.junit.Assert.assertTrue(Assert.java:41)
¶	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase.java:82)
¶	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase.java:167)
¶	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase.java:204)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
¶	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
¶	at java.desktop/java.awt.Component.processMouseEvent(Component.java:6629)
¶	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="263" _type="ConsoleOutput" date="Wed Jul 09 22:45:52 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2367883" type="ConsoleOutput">
    <outputString><![CDATA[	at java.desktop/java.awt.Component.processEvent(Component.java:6391)
	at java.desktop/java.awt.Container.processEvent(Container.java:2266)
	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
	at java.desktop/java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948)
	at java.desktop/java.awt.LightweightDispatcher.processMouseEvent(Container.java:4584)
	at java.desktop/java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516)
	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2310)
	at java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2780)
	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:97)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:747)
	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:745)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:744)
	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)
>>Steps traced since last test:

>>ConsoleSceneView test execution time (ms):32<<
>>Test Result:
ConsoleSceneView,0.0% complete,0.0,50.0,No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. 
<<
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,"	at "), Diff(EQUAL,"java."), Diff(DELETE,"lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0
¶	at org.junit.Assert.fail(Asser"), Diff(INSERT,"desktop/java.awt.Component.processEvent(Componen"), Diff(EQUAL,"t.java:"), Diff(DELETE,"88"), Diff(INSERT,"6391"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"org.junit.Assert.assertTrue(Assert.java:41)
¶	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase"), Diff(INSERT,"java.desktop/java.awt.Container.processEvent(Container"), Diff(EQUAL,".java:"), Diff(DELETE,"8"), Diff(EQUAL,"2"), Diff(INSERT,"266"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase"), Diff(INSERT,"java.desktop/java.awt.Component.dispatchEventImpl(Component"), Diff(EQUAL,".java:"), Diff(DELETE,"167"), Diff(INSERT,"5001"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase"), Diff(INSERT,"java.desktop/java.awt.Container.dispatchEventImpl(Container"), Diff(EQUAL,".java:2"), Diff(DELETE,"0"), Diff(INSERT,"32"), Diff(EQUAL,"4)
¶	at "), Diff(DELETE,"gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTes"), Diff(INSERT,"java.desktop/java.awt.Component.dispatchEvent(Componen"), Diff(EQUAL,"t.java:"), Diff(DELETE,"771"), Diff(INSERT,"4833"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest"), Diff(INSERT,"java.desktop/java.awt.LightweightDispatcher.retargetMouseEvent(Container"), Diff(EQUAL,".java:"), Diff(DELETE,"764"), Diff(INSERT,"4948"), Diff(EQUAL,")
¶	at java."), Diff(DELETE,"base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl"), Diff(INSERT,"desktop/java.awt.LightweightDispatcher.processMouseEvent(Container"), Diff(EQUAL,".java:"), Diff(DELETE,"77"), Diff(INSERT,"4584"), Diff(EQUAL,")
¶	at java."), Diff(DELETE,"base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl"), Diff(INSERT,"desktop/java.awt.LightweightDispatcher.dispatchEvent(Container"), Diff(EQUAL,".java:4"), Diff(DELETE,"3"), Diff(INSERT,"516"), Diff(EQUAL,")
¶	at java."), Diff(DELETE,"base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod"), Diff(INSERT,"desktop/java.awt.Container.dispatchEventImpl(Container"), Diff(EQUAL,".java:"), Diff(DELETE,"5"), Diff(INSERT,"231"), Diff(EQUAL,"0)
¶	at "), Diff(DELETE,"org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod"), Diff(INSERT,"java.desktop/java.awt.Window.dispatchEventImpl(Window"), Diff(EQUAL,".java:"), Diff(DELETE,"47"), Diff(INSERT,"2780"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner"), Diff(INSERT,"java.desktop/java.awt.Component.dispatchEvent(Component"), Diff(EQUAL,".java:"), Diff(DELETE,"325"), Diff(INSERT,"4833"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner"), Diff(INSERT,"java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue"), Diff(EQUAL,".java:"), Diff(DELETE,"290"), Diff(INSERT,"775"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner"), Diff(INSERT,"java.desktop/java.awt.EventQueue$4.run(EventQueue"), Diff(EQUAL,".java:"), Diff(DELETE,"288"), Diff(INSERT,"720"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner"), Diff(INSERT,"java.desktop/java.awt.EventQueue$4.run(EventQueue"), Diff(EQUAL,".java:"), Diff(DELETE,"268"), Diff(INSERT,"714"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.jun"), Diff(INSERT,"java.base/java.secur"), Diff(EQUAL,"it"), Diff(INSERT,"y"), Diff(EQUAL,".A"), Diff(DELETE,"GradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite"), Diff(INSERT,"ccessController.doPrivileged(AccessController"), Diff(EQUAL,".java:"), Diff(DELETE,"193"), Diff(INSERT,"399"), Diff(EQUAL,")
¶	at java.base/j"), Diff(DELETE,"dk.internal.reflect.NativeMethod"), Diff(INSERT,"ava.security.ProtectionDomain$JavaSecurity"), Diff(EQUAL,"Access"), Diff(DELETE,"or"), Diff(EQUAL,"Impl."), Diff(DELETE,"invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl"), Diff(INSERT,"doIntersectionPrivilege(ProtectionDomain"), Diff(EQUAL,".java:"), Diff(DELETE,"77"), Diff(INSERT,"86"), Diff(EQUAL,")
¶	at java.base/j"), Diff(DELETE,"dk.internal.reflect.DelegatingMethod"), Diff(INSERT,"ava.security.ProtectionDomain$JavaSecurity"), Diff(EQUAL,"Access"), Diff(DELETE,"or"), Diff(EQUAL,"Impl."), Diff(DELETE,"invoke(DelegatingMethodAccessorImpl"), Diff(INSERT,"doIntersectionPrivilege(ProtectionDomain"), Diff(EQUAL,".java:"), Diff(DELETE,"43"), Diff(INSERT,"97"), Diff(EQUAL,")
¶	at java."), Diff(DELETE,"base/java.lang.reflect.Method.invoke(Method"), Diff(INSERT,"desktop/java.awt.EventQueue$5.run(EventQueue"), Diff(EQUAL,".java:"), Diff(DELETE,"568"), Diff(INSERT,"747"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod"), Diff(INSERT,"java.desktop/java.awt.EventQueue$5.run(EventQueue"), Diff(EQUAL,".java:"), Diff(DELETE,"219"), Diff(INSERT,"745"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManag"), Diff(INSERT,"java.base/java.security.AccessController.doPrivileged(AccessControll"), Diff(EQUAL,"er.java:"), Diff(DELETE,"1444"), Diff(INSERT,"399"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager"), Diff(INSERT,"java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain"), Diff(EQUAL,".java:"), Diff(DELETE,"190"), Diff(INSERT,"8"), Diff(EQUAL,"6)
¶	at "), Diff(DELETE,"bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager"), Diff(INSERT,"java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue"), Diff(EQUAL,".java:"), Diff(DELETE,"1585"), Diff(INSERT,"744"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager"), Diff(INSERT,"java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread"), Diff(EQUAL,".java:"), Diff(DELETE,"1538"), Diff(INSERT,"203"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager"), Diff(INSERT,"java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread"), Diff(EQUAL,".java:1"), Diff(INSERT,"2"), Diff(EQUAL,"4"), Diff(DELETE,"06"), Diff(EQUAL,")
¶	at "), Diff(DELETE,"bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
¶	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter"), Diff(INSERT,"java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread"), Diff(EQUAL,".java:"), Diff(DELETE,"469"), Diff(INSERT,"113"), Diff(EQUAL,")
¶	at java.desktop/java.awt."), Diff(DELETE,"AWT"), Diff(EQUAL,"Event"), Diff(DELETE,"Multicaster.mouseClicked(AWTEventMulticaster"), Diff(INSERT,"DispatchThread.pumpEvents(EventDispatchThread"), Diff(EQUAL,".java:"), Diff(DELETE,"278"), Diff(INSERT,"109"), Diff(EQUAL,")
¶	at java.desktop/java.awt."), Diff(DELETE,"AWT"), Diff(EQUAL,"Event"), Diff(DELETE,"Multicaster.mouseClicked(AWTEventMulticaster"), Diff(INSERT,"DispatchThread.pumpEvents(EventDispatchThread"), Diff(EQUAL,".java:"), Diff(DELETE,"277"), Diff(INSERT,"101"), Diff(EQUAL,")
¶	at java.desktop/java.awt."), Diff(DELETE,"Component.processMouseEvent(Component.java:6629)
¶	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)"), Diff(INSERT,"EventDispatchThread.run(EventDispatchThread.java:90)
¶>>Steps traced since last test:
¶
¶>>ConsoleSceneView test execution time (ms):32<<
¶>>Test Result:
¶ConsoleSceneView,0.0% complete,0.0,50.0,No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. 
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="264" _type="ShellCommand" date="Wed Jul 09 22:46:23 EDT 2025" starttimestamp="1752113184173" timestamp="2399002" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="265" _type="ShellCommand" date="Wed Jul 09 22:46:29 EDT 2025" starttimestamp="1752113184173" timestamp="2405605" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="266" _type="ShellCommand" date="Wed Jul 09 22:46:34 EDT 2025" starttimestamp="1752113184173" timestamp="2410727" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="267" _type="ShellCommand" date="Wed Jul 09 22:46:37 EDT 2025" starttimestamp="1752113184173" timestamp="2412920" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="268" _type="ConsoleOutput" date="Wed Jul 09 22:46:39 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2415747" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Inheritance
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"	at java.desktop/java.awt.Component.processEvent(Component.java:6391)
¶	at java.desktop/java.awt.Container.processEvent(Container.java:2266)
¶	at java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5001)
¶	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
¶	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
¶	at java.desktop/java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948)
¶	at java.desktop/java.awt.LightweightDispatcher.processMouseEvent(Container.java:4584)
¶	at java.desktop/java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516)
¶	at java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2310)
¶	at java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2780)
¶	at java.desktop/java.awt.Component.dispatchEvent(Component.java:4833)
¶	at java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775)
¶	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:720)
¶	at java.desktop/java.awt.EventQueue$4.run(EventQueue.java:714)
¶	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:97)
¶	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:747)
¶	at java.desktop/java.awt.EventQueue$5.run(EventQueue.java:745)
¶	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
¶	at java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:86)
¶	at java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:744)
¶	at java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109)
¶	at java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101)
¶	at java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:90)
¶>>Steps traced since last test:
¶
¶>>ConsoleSceneView test execution time (ms):32<<
¶>>Test Result:
¶ConsoleSceneView,0.0% complete,0.0,50.0,No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. "), Diff(INSERT,">>Running suite A2Inheritance"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="269" _type="ConsoleOutput" date="Wed Jul 09 22:46:39 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2415754" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:46:39 EDT 2025<<
>>Running test LocatableXProperty
<<
>>LocatableXProperty test execution time (ms):1<<
>>Test Result:
LocatableXProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:46:39 EDT 2025<<
>>Running test LocatableYProperty
<<
>>LocatableYProperty test execution time (ms):1<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2Inheritance
¶"), Diff(INSERT,"Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test LocatableXProperty
¶<<
¶>>LocatableXProperty test execution time (ms):1<<
¶>>Test Result:
¶LocatableXProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test LocatableYProperty
¶<<
¶>>LocatableYProperty test execution time (ms):1"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="270" _type="ConsoleOutput" date="Wed Jul 09 22:46:39 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2415758" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
LocatableYProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:46:39 EDT 2025<<
>>Running test LocatableXEditableProperty
<<
>>LocatableXEditableProperty test execution time (ms):0<<
>>Test Result:
LocatableXEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(INSERT,">>Test Result:
¶LocatableYProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶"), Diff(EQUAL,">>Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test LocatableX"), Diff(INSERT,"Editable"), Diff(EQUAL,"Property
¶<<
¶>>LocatableX"), Diff(INSERT,"Editable"), Diff(EQUAL,"Property test execution time (ms):"), Diff(DELETE,"1"), Diff(INSERT,"0"), Diff(EQUAL,"<<
¶>>Test Result:
¶LocatableX"), Diff(INSERT,"Editable"), Diff(EQUAL,"Property,0.0% complete,0.0,2.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶"), Diff(DELETE,">>Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test LocatableYProperty
¶<<
¶>>LocatableYProperty test execution time (ms):1<<
¶")]]]></diff>
  </Command>
  <Command __id="271" _type="ConsoleOutput" date="Wed Jul 09 22:46:39 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2415762" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:46:39 EDT 2025<<
>>Running test LocatableYEditableProperty
<<
>>LocatableYEditableProperty test execution time (ms):1<<
>>Test Result:
LocatableYEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:46:39 EDT 2025<<
>>Running test TaggedBoundedShape
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Test Result:
¶LocatableYProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶"), Diff(EQUAL,">>Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test Locatable"), Diff(DELETE,"X"), Diff(INSERT,"Y"), Diff(EQUAL,"EditableProperty
¶<<
¶>>Locatable"), Diff(DELETE,"X"), Diff(INSERT,"Y"), Diff(EQUAL,"EditableProperty test execution time (ms):"), Diff(DELETE,"0"), Diff(INSERT,"1"), Diff(EQUAL,"<<
¶>>Test Result:
¶Locatable"), Diff(DELETE,"X"), Diff(INSERT,"Y"), Diff(EQUAL,"EditableProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶"), Diff(INSERT,">>Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test TaggedBoundedShape
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="272" _type="ConsoleOutput" date="Wed Jul 09 22:46:39 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2415768" type="ConsoleOutput">
    <outputString><![CDATA[>>Steps traced since last test:

>>TaggedBoundedShape test execution time (ms):2<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,No class in project matching name/tag:BoundedShape
<<
>>Wed Jul 09 22:46:39 EDT 2025<<
>>Running test BoundedShapeWidthProperty
<<
>>BoundedShapeWidthProperty test execution time (ms):0<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test LocatableYEditableProperty
¶<<
¶>>LocatableYEditableProperty"), Diff(INSERT,"Steps traced since last test:
¶
¶>>TaggedBoundedShape"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"1"), Diff(INSERT,"2"), Diff(EQUAL,"<<
¶>>Test Result:
¶"), Diff(DELETE,"LocatableYEditableProperty"), Diff(INSERT,"TaggedBoundedShape"), Diff(EQUAL,",0.0% complete,0.0,"), Diff(DELETE,"2"), Diff(INSERT,"0"), Diff(EQUAL,".0,"), Diff(DELETE,"
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test TaggedBoundedShape
¶"), Diff(INSERT,"No class in project matching name/tag:BoundedShape
¶<<
¶>>Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test BoundedShapeWidthProperty
¶<<
¶>>BoundedShapeWidthProperty test execution time (ms):0"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="273" _type="ConsoleOutput" date="Wed Jul 09 22:46:39 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2415781" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Wed Jul 09 22:46:39 EDT 2025<<
>>Running test BoundedShapeHeightProperty
<<
>>BoundedShapeHeightProperty test execution time (ms):0<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Wed Jul 09 22:46:39 EDT 2025<<
>>Running test BoundedShapeWidthEditableProperty
<<
>>BoundedShapeWidthEditableProperty test execution time (ms):0<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Wed Jul 09 22:46:39 EDT 2025<<
>>Running test BoundedShapeHeightEditableProperty
<<
>>BoundedShapeHeightEditableProperty test execution time (ms):0<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Wed Jul 09 22:46:39 EDT 2025<<
>>Running test A2ExpectedSuperTypes
<<
>>A2ExpectedSuperTypes test execution time (ms):0<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Steps traced since last test:
¶
¶>>TaggedBoundedShape"), Diff(INSERT,"Test Result:
¶BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedBoundedShape failed.
¶Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
¶<<
¶>>Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test BoundedShapeHeightProperty
¶<<
¶>>BoundedShapeHeightProperty"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"2"), Diff(INSERT,"0"), Diff(EQUAL,"<<
¶>>Test Result:
¶"), Diff(DELETE,"Tagged"), Diff(EQUAL,"BoundedShape"), Diff(INSERT,"HeightProperty"), Diff(EQUAL,",0.0% complete,0.0,"), Diff(DELETE,"0"), Diff(INSERT,"2"), Diff(EQUAL,".0,"), Diff(DELETE,"No class in project matching name/tag:BoundedShape"), Diff(INSERT,"
¶Preceding test TaggedBoundedShape failed.
¶Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
¶<<
¶>>Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test BoundedShapeWidthEditableProperty
¶<<
¶>>BoundedShapeWidthEditableProperty test execution time (ms):0<<
¶>>Test Result:
¶BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedBoundedShape failed.
¶Please correct the problems identified by preceding test:TaggedBoundedShape before running this test"), Diff(EQUAL,"
¶<<
¶>>Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test BoundedShape"), Diff(DELETE,"Width"), Diff(INSERT,"HeightEditable"), Diff(EQUAL,"Property
¶<<
¶>>BoundedShape"), Diff(DELETE,"WidthProperty"), Diff(INSERT,"HeightEditableProperty test execution time (ms):0<<
¶>>Test Result:
¶BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedBoundedShape failed.
¶Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
¶<<
¶>>Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test A2ExpectedSuperTypes
¶<<
¶>>A2ExpectedSuperTypes"), Diff(EQUAL," test execution time (ms):0<<
¶")]]]></diff>
  </Command>
  <Command __id="274" _type="ConsoleOutput" date="Wed Jul 09 22:46:39 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2415794" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
Preceding test LocatableXProperty failed.
Please correct the problems identified by preceding test:LocatableXProperty before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Test Result:
¶"), Diff(DELETE,"BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedBoundedShape failed.
¶Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
¶<<
¶>>Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test BoundedShapeHeightProperty
¶<<
¶>>BoundedShapeHeightProperty test execution time (ms):0<<
¶>>Test Result:
¶BoundedShapeHeightProperty"), Diff(INSERT,"A2ExpectedSuperTypes"), Diff(EQUAL,",0.0% complete,0.0,2"), Diff(INSERT,"0"), Diff(EQUAL,".0,
¶Preceding test "), Diff(DELETE,"TaggedBoundedShape failed.
¶Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
¶<<
¶>>Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test BoundedShapeWidthEditableProperty
¶<<
¶>>BoundedShapeWidthEditableProperty test execution time (ms):0<<
¶>>Test Result:
¶BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedBoundedShape failed.
¶Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
¶<<
¶>>Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test BoundedShapeHeightEditableProperty
¶<<
¶>>BoundedShapeHeightEditableProperty test execution time (ms):0<<
¶>>Test Result:
¶BoundedShapeHeightEdi"), Diff(INSERT,"LocatableXProperty failed.
¶Please correct the problems identified by preceding test:Loca"), Diff(EQUAL,"table"), Diff(INSERT,"X"), Diff(EQUAL,"Property"), Diff(DELETE,",0.0% complete,0.0,2.0,
¶Preceding test TaggedBoundedShape failed.
¶Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
¶<<
¶>>Wed Jul 09 22:46:39 EDT 2025<<
¶>>Running test A2ExpectedSuperTypes
¶<<
¶>>A2ExpectedSuperTypes test execution time (ms):0"), Diff(INSERT," before running this test
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="275" _type="ConsoleOutput" date="Wed Jul 09 22:46:43 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419821" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Style
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2ExpectedSuperTypes,0.0% complete,0.0,20.0,
¶Preceding test LocatableXProperty failed.
¶Please correct the problems identified by preceding test:LocatableXProperty before running this test"), Diff(INSERT,"Running suite A2Style"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="276" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419831" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:46:43 EDT 2025<<
>>Running test A2PackageDeclarations
<<
>>Wed Jul 09 22:46:43 EDT 2025<<
>>Running test BridgeSceneDynamics
<<
>>Wed Jul 09 22:46:43 EDT 2025<<
>>Running test BridgeSceneApproachMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2Style"), Diff(INSERT,"Wed Jul 09 22:46:43 EDT 2025<<
¶>>Running test A2PackageDeclarations
¶<<
¶>>Wed Jul 09 22:46:43 EDT 2025<<
¶>>Running test BridgeSceneDynamics
¶<<
¶>>Wed Jul 09 22:46:43 EDT 2025<<
¶>>Running test BridgeSceneApproachMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="277" _type="LocalChecksRawCommand" date="Wed Jul 09 22:46:44 EDT 2025" starttimestamp="1752113184173" timestamp="2419858">
    <CSVRow><![CDATA[359,Wed Jul 09 22:45:51 EDT 2025,45,0,A2ConsoleSceneView,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,195,2,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView ,TaggedLocatable , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="278" _type="LocalChecksRawCommand" date="Wed Jul 09 22:46:44 EDT 2025" starttimestamp="1752113184173" timestamp="2419859">
    <CSVRow><![CDATA[360,Wed Jul 09 22:46:39 EDT 2025,45,0,A2Inheritance,A2CommonSignaturesAreInherited A2Encapsulation A2InterfaceAsType A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamedConstants A2NamingConventions A2NoStarImports A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields A2NonPublicAccessModifiersMatched BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,195,3,true,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , , ,A2ExpectedSuperTypes-(0.0/20.0) BoundedShapeHeightEditableProperty-(0.0/2.0) BoundedShapeHeightProperty-(0.0/2.0) BoundedShapeWidthEditableProperty-(0.0/2.0) BoundedShapeWidthProperty-(0.0/2.0) LocatableXEditableProperty-(0.0/2.0) LocatableXProperty-(0.0/2.0) LocatableYEditableProperty-(0.0/2.0) LocatableYProperty-(0.0/2.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="279" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419863" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneApproachMethodDefined test execution time (ms):37<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:46:43 EDT 2025<<
¶>>Running test A2PackageDeclarations
¶<<
¶>>Wed Jul 09 22:46:43 EDT 2025<<
¶>>Running test BridgeSceneDynamics
¶<<
¶>>Wed Jul 09 22:46:43 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeSceneApproachMethodDefined"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):37"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="280" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419867" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneApproachMethodDefined"), Diff(DELETE," test execution time (ms):37"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="281" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419870" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:46:44 EDT 2025<<
>>Running test BridgeSceneSayMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 22:46:44 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"Approach"), Diff(INSERT,"Say"), Diff(EQUAL,"MethodDefined"), Diff(DELETE,",100.0% complete,2.0,2.0,"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="282" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419888" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneSayMethodDefined test execution time (ms):20<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:46:44 EDT 2025<<
¶>>Running test BridgeSceneSayMethodDefined
¶"), Diff(INSERT,"BridgeSceneSayMethodDefined test execution time (ms):20"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="283" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419901" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:46:44 EDT 2025<<
>>Running test BridgeScenePassedMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneSayMethodDefined"), Diff(DELETE," test execution time (ms):20"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:46:44 EDT 2025<<
¶>>Running test BridgeScenePassedMethodDefined
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="284" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419917" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeScenePassedMethodDefined test execution time (ms):23<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"Say"), Diff(INSERT,"Passed"), Diff(EQUAL,"MethodDefined"), Diff(DELETE,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:46:44 EDT 2025<<
¶>>Running test BridgeScenePassedMethodDefined
¶"), Diff(INSERT," test execution time (ms):23"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="285" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419919" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 22:46:44 EDT 2025<<
>>Running test BridgeSceneFailedMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeScenePassedMethodDefined"), Diff(DELETE," test execution time (ms):23"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:46:44 EDT 2025<<
¶>>Running test BridgeSceneFailedMethodDefined
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="286" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419939" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneFailedMethodDefined test execution time (ms):20<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"Pass"), Diff(INSERT,"Fail"), Diff(EQUAL,"edMethodDefined"), Diff(DELETE,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 22:46:44 EDT 2025<<
¶>>Running test BridgeSceneFailedMethodDefined
¶"), Diff(INSERT," test execution time (ms):20"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="287" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419944" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneFailedMethodDefined"), Diff(DELETE," test execution time (ms):20"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="288" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419959" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneDynamics test execution time (ms):40<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
¶"), Diff(INSERT,"BridgeSceneDynamics test execution time (ms):40"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="289" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419965" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneDynamics"), Diff(DELETE," test execution time (ms):40"), Diff(INSERT,",100.0% complete,50.0,50.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="290" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419977" type="ConsoleOutput">
    <outputString><![CDATA[>>A2PackageDeclarations test execution time (ms):58<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶BridgeSceneDynamics,100.0% complete,50.0,50.0,
¶"), Diff(INSERT,"A2PackageDeclarations test execution time (ms):58"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="291" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419981" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2PackageDeclarations"), Diff(DELETE," test execution time (ms):58"), Diff(INSERT,",100.0% complete,5.0,5.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="292" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419983" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:46:44 EDT 2025<<
>>Running test A2SimplifyBooleanExpressions
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2PackageDeclarations,100.0% complete,5.0,5.0,"), Diff(INSERT,"Wed Jul 09 22:46:44 EDT 2025<<
¶>>Running test A2SimplifyBooleanExpressions"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="293" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419991" type="ConsoleOutput">
    <outputString><![CDATA[>>A2SimplifyBooleanExpressions test execution time (ms):9<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:46:44 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"A2SimplifyBooleanExpressions"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):9"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="294" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419995" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2SimplifyBooleanExpressions"), Diff(DELETE," test execution time (ms):9"), Diff(INSERT,",100.0% complete,5.0,5.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="295" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2419997" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:46:44 EDT 2025<<
>>Running test A2SimplifyBooleanReturns
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,"), Diff(INSERT,"Wed Jul 09 22:46:44 EDT 2025<<
¶>>Running test A2SimplifyBooleanReturns"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="296" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2420007" type="ConsoleOutput">
    <outputString><![CDATA[>>A2SimplifyBooleanReturns test execution time (ms):10<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:46:44 EDT 2025<<
¶>>Running test A2SimplifyBooleanReturns
¶"), Diff(INSERT,"A2SimplifyBooleanReturns test execution time (ms):10"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="297" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2420020" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Wed Jul 09 22:46:44 EDT 2025<<
>>Running test A2NoHiddenFields
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2SimplifyBooleanReturns"), Diff(DELETE," test execution time (ms):10"), Diff(INSERT,",100.0% complete,5.0,5.0,
¶<<
¶>>Wed Jul 09 22:46:44 EDT 2025<<
¶>>Running test A2NoHiddenFields
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="298" _type="ConsoleOutput" date="Wed Jul 09 22:46:44 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2420030" type="ConsoleOutput">
    <outputString><![CDATA[>>Steps traced since last test:

>>A2NoHiddenFields test execution time (ms):17<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
¶<<
¶>>Wed Jul 09 22:46:44 EDT 2025<<
¶>>Running test A2NoHiddenFields
¶"), Diff(INSERT,"Steps traced since last test:
¶
¶>>A2NoHiddenFields test execution time (ms):17"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="316" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:50:40 EDT 2025" kind="HitBreakPoint" projectName="Assn2" starttimestamp="1752113184173" timestamp="2656215" type="Run" />
  <Command __id="317" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:50:40 EDT 2025" kind="Terminate" projectName="Assn2" starttimestamp="1752113184173" timestamp="2656215" type="Run" />
  <Command __id="318" _type="ShellCommand" date="Wed Jul 09 22:51:40 EDT 2025" starttimestamp="1752113184173" timestamp="2716452" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="319" _type="EclipseCommand" commandID="org.eclipse.ui.file.refresh" date="Wed Jul 09 22:51:45 EDT 2025" starttimestamp="1752113184173" timestamp="2721450" />
  <Command __id="320" _type="EclipseCommand" commandID="" date="Wed Jul 09 22:51:45 EDT 2025" starttimestamp="1752113184173" timestamp="2721666" />
  <Command __id="321" _type="EclipseCommand" commandID="" date="Wed Jul 09 22:51:45 EDT 2025" starttimestamp="1752113184173" timestamp="2721735" />
  <Command __id="322" _type="ShellCommand" date="Wed Jul 09 22:51:53 EDT 2025" starttimestamp="1752113184173" timestamp="2729566" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="328" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:51:59 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752113184173" timestamp="2735668" type="Run" />
  <Command __id="329" _type="RunCommand" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 22:51:59 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752113184173" timestamp="2735668" type="Run" />
  <Command __id="330" _type="ShellCommand" date="Wed Jul 09 22:52:04 EDT 2025" starttimestamp="1752113184173" timestamp="2740302" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="331" _type="ConsoleOutput" date="Wed Jul 09 22:52:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2754238" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Inheritance
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2CommonSignaturesAreInherited,87.82608695652175% complete,6.1,7.0,See console trace about lines failing  this check"), Diff(INSERT,"Running suite A2Inheritance"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="332" _type="ConsoleOutput" date="Wed Jul 09 22:52:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2754286" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:52:18 EDT 2025<<
>>Running test TaggedLocatable
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2Inheritanc"), Diff(INSERT,"Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test TaggedLocatabl"), Diff(EQUAL,"e
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="333" _type="ConsoleOutput" date="Wed Jul 09 22:52:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2754308" type="ConsoleOutput">
    <outputString><![CDATA[>>Steps traced since last test:
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test TaggedLocatable
¶<<"), Diff(INSERT,"Steps traced since last test:"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="334" _type="ConsoleOutput" date="Wed Jul 09 22:52:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2754316" type="ConsoleOutput">
    <outputString><![CDATA[>>TaggedLocatable test execution time (ms):43<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Steps traced since last test:"), Diff(INSERT,"TaggedLocatable test execution time (ms):43<<
¶>>Test Result:
¶TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="335" _type="ConsoleOutput" date="Wed Jul 09 22:52:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2754330" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 22:52:18 EDT 2025<<
>>Running test LocatableXProperty
<<
>>LocatableXProperty test execution time (ms):0<<
>>Test Result:
LocatableXProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:52:18 EDT 2025<<
>>Running test LocatableYProperty
<<
>>LocatableYProperty test execution time (ms):1<<
>>Test Result:
LocatableYProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:52:18 EDT 2025<<
>>Running test LocatableXEditableProperty
<<
>>LocatableXEditableProperty test execution time (ms):1<<
>>Test Result:
LocatableXEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:52:18 EDT 2025<<
>>Running test LocatableYEditableProperty
<<
>>LocatableYEditableProperty test execution time (ms):1<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"TaggedLocatable"), Diff(INSERT,"Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test LocatableXProperty
¶<<
¶>>LocatableXProperty"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"43"), Diff(INSERT,"0"), Diff(EQUAL,"<<
¶>>Test Result:
¶"), Diff(DELETE,"Tagged"), Diff(EQUAL,"Locatable"), Diff(INSERT,"XProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test LocatableYProperty
¶<<
¶>>LocatableYProperty test execution time (ms):1<<
¶>>Test Result:
¶LocatableYProperty"), Diff(EQUAL,",0.0% complete,0.0,"), Diff(DELETE,"0"), Diff(INSERT,"2"), Diff(EQUAL,".0,"), Diff(DELETE,"No class in project matching name/tag:Locatable
¶"), Diff(INSERT,"
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test LocatableXEditableProperty
¶<<
¶>>LocatableXEditableProperty test execution time (ms):1<<
¶>>Test Result:
¶LocatableXEditableProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test LocatableYEditableProperty
¶<<
¶>>LocatableYEditableProperty test execution time (ms):1"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="336" _type="ConsoleOutput" date="Wed Jul 09 22:52:18 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="2754376" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
LocatableYEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 22:52:18 EDT 2025<<
>>Running test TaggedBoundedShape
<<
>>Steps traced since last test:

>>TaggedBoundedShape test execution time (ms):3<<
>>Test Result:
TaggedBoundedShape,0.0% complete,0.0,0.0,No class in project matching name/tag:BoundedShape
<<
>>Wed Jul 09 22:52:18 EDT 2025<<
>>Running test BoundedShapeWidthProperty
<<
>>BoundedShapeWidthProperty test execution time (ms):1<<
>>Test Result:
BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Wed Jul 09 22:52:18 EDT 2025<<
>>Running test BoundedShapeHeightProperty
<<
>>BoundedShapeHeightProperty test execution time (ms):0<<
>>Test Result:
BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Wed Jul 09 22:52:18 EDT 2025<<
>>Running test BoundedShapeWidthEditableProperty
<<
>>BoundedShapeWidthEditableProperty test execution time (ms):1<<
>>Test Result:
BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
>>Wed Jul 09 22:52:18 EDT 2025<<
>>Running test BoundedShapeHeightEditableProperty
<<
>>BoundedShapeHeightEditableProperty test execution time (ms):1<<
>>Test Result:
BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
Preceding test TaggedBoundedShape failed.
Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test LocatableXProperty
¶<<
¶>>LocatableX"), Diff(INSERT,"Test Result:
¶LocatableYEditableProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test TaggedBoundedShape
¶<<
¶>>Steps traced since last test:
¶
¶>>TaggedBoundedShape test execution time (ms):3<<
¶>>Test Result:
¶TaggedBoundedShape,0.0% complete,0.0,0.0,No class in project matching name/tag:BoundedShape
¶<<
¶>>Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test BoundedShapeWidthProperty
¶<<
¶>>BoundedShapeWidth"), Diff(EQUAL,"Property test execution time (ms):"), Diff(DELETE,"0"), Diff(INSERT,"1"), Diff(EQUAL,"<<
¶>>Test Result:
¶"), Diff(DELETE,"LocatableX"), Diff(INSERT,"BoundedShapeWidth"), Diff(EQUAL,"Property,0.0% complete,0.0,2.0,
¶Preceding test Tagged"), Diff(DELETE,"Locatabl"), Diff(INSERT,"BoundedShap"), Diff(EQUAL,"e failed.
¶Please correct the problems identified by preceding test:Tagged"), Diff(DELETE,"Locatabl"), Diff(INSERT,"BoundedShap"), Diff(EQUAL,"e before running this test
¶<<
¶>>Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test "), Diff(DELETE,"LocatableYProperty
¶<<
¶>>LocatableY"), Diff(INSERT,"BoundedShapeHeightProperty
¶<<
¶>>BoundedShapeHeight"), Diff(EQUAL,"Property test execution time (ms):"), Diff(DELETE,"1"), Diff(INSERT,"0"), Diff(EQUAL,"<<
¶>>Test Result:
¶"), Diff(DELETE,"LocatableY"), Diff(INSERT,"BoundedShapeHeight"), Diff(EQUAL,"Property,0.0% complete,0.0,2.0,
¶Preceding test Tagged"), Diff(DELETE,"Locatabl"), Diff(INSERT,"BoundedShap"), Diff(EQUAL,"e failed.
¶Please correct the problems identified by preceding test:Tagged"), Diff(DELETE,"Locatabl"), Diff(INSERT,"BoundedShap"), Diff(EQUAL,"e before running this test
¶<<
¶>>Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test "), Diff(DELETE,"LocatableX"), Diff(INSERT,"BoundedShapeWidth"), Diff(EQUAL,"EditableProperty
¶<<
¶>>"), Diff(DELETE,"LocatableX"), Diff(INSERT,"BoundedShapeWidth"), Diff(EQUAL,"EditableProperty test execution time (ms):1<<
¶>>Test Result:
¶"), Diff(DELETE,"LocatableX"), Diff(INSERT,"BoundedShapeWidth"), Diff(EQUAL,"EditableProperty,0.0% complete,0.0,2.0,
¶Preceding test Tagged"), Diff(DELETE,"Locatabl"), Diff(INSERT,"BoundedShap"), Diff(EQUAL,"e failed.
¶Please correct the problems identified by preceding test:Tagged"), Diff(DELETE,"Locatabl"), Diff(INSERT,"BoundedShap"), Diff(EQUAL,"e before running this test
¶<<
¶>>Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test "), Diff(DELETE,"LocatableY"), Diff(INSERT,"BoundedShapeHeight"), Diff(EQUAL,"EditableProperty
¶<<
¶>>"), Diff(DELETE,"LocatableY"), Diff(INSERT,"BoundedShapeHeight"), Diff(EQUAL,"EditableProperty test execution time (ms):1<<
¶"), Diff(INSERT,">>Test Result:
¶BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedBoundedShape failed.
¶Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="338" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 23:06:32 EDT 2025" kind="HitBreakPoint" projectName="Assn2" starttimestamp="1752113184173" timestamp="3608557" type="Run" />
  <Command __id="339" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 23:06:32 EDT 2025" kind="Terminate" projectName="Assn2" starttimestamp="1752113184173" timestamp="3608558" type="Run" />
  <Command __id="340" _type="ShellCommand" date="Wed Jul 09 23:07:24 EDT 2025" starttimestamp="1752113184173" timestamp="3660523" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="341" _type="ShellCommand" date="Wed Jul 09 23:07:25 EDT 2025" starttimestamp="1752113184173" timestamp="3661538" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="342" _type="ShellCommand" date="Wed Jul 09 23:07:26 EDT 2025" starttimestamp="1752113184173" timestamp="3662603" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="343" _type="EclipseCommand" commandID="org.eclipse.ui.file.refresh" date="Wed Jul 09 23:07:31 EDT 2025" starttimestamp="1752113184173" timestamp="3667667" />
  <Command __id="344" _type="EclipseCommand" commandID="" date="Wed Jul 09 23:07:32 EDT 2025" starttimestamp="1752113184173" timestamp="3667871" />
  <Command __id="345" _type="EclipseCommand" commandID="" date="Wed Jul 09 23:07:32 EDT 2025" starttimestamp="1752113184173" timestamp="3667873" />
  <Command __id="346" _type="ShellCommand" date="Wed Jul 09 23:07:36 EDT 2025" starttimestamp="1752113184173" timestamp="3672089" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="352" _type="ProgramExecutionEvent" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 23:07:44 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752113184173" timestamp="3680122" type="Run" />
  <Command __id="353" _type="RunCommand" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 23:07:44 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752113184173" timestamp="3680122" type="Run" />
  <Command __id="354" _type="LocalChecksRawCommand" date="Wed Jul 09 23:07:44 EDT 2025" starttimestamp="1752113184173" timestamp="3680163">
    <CSVRow><![CDATA[362,Wed Jul 09 22:52:18 EDT 2025,41,0,A2Inheritance,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2InterfaceAsType A2NamedConstants A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,196,0,true,A2ExpectedSuperTypes BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeWidthEditableProperty BoundedShapeWidthProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , , ,A2ExpectedSuperTypes-(0.0/20.0) BoundedShapeHeightEditableProperty-(0.0/2.0) BoundedShapeHeightProperty-(0.0/2.0) BoundedShapeWidthEditableProperty-(0.0/2.0) BoundedShapeWidthProperty-(0.0/2.0) LocatableXEditableProperty-(0.0/2.0) LocatableXProperty-(0.0/2.0) LocatableYEditableProperty-(0.0/2.0) LocatableYProperty-(0.0/2.0) TaggedBoundedShape-(0.0/0.0) TaggedLocatable-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="355" _type="ShellCommand" date="Wed Jul 09 23:07:48 EDT 2025" starttimestamp="1752113184173" timestamp="3684571" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="356" _type="ConsoleOutput" date="Wed Jul 09 23:08:10 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3706650" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Factory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶LocatableYEditableProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test TaggedBoundedShape
¶<<
¶>>Steps traced since last test:
¶
¶>>TaggedBoundedShape test execution time (ms):3<<
¶>>Test Result:
¶TaggedBoundedShape,0.0% complete,0.0,0.0,No class in project matching name/tag:BoundedShape
¶<<
¶>>Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test BoundedShapeWidthProperty
¶<<
¶>>BoundedShapeWidthProperty test execution time (ms):1<<
¶>>Test Result:
¶BoundedShapeWidthProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedBoundedShape failed.
¶Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
¶<<
¶>>Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test BoundedShapeHeightProperty
¶<<
¶>>BoundedShapeHeightProperty test execution time (ms):0<<
¶>>Test Result:
¶BoundedShapeHeightProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedBoundedShape failed.
¶Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
¶<<
¶>>Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test BoundedShapeWidthEditableProperty
¶<<
¶>>BoundedShapeWidthEditableProperty test execution time (ms):1<<
¶>>Test Result:
¶BoundedShapeWidthEditableProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedBoundedShape failed.
¶Please correct the problems identified by preceding test:TaggedBoundedShape before running this test
¶<<
¶>>Wed Jul 09 22:52:18 EDT 2025<<
¶>>Running test BoundedShapeHeightEditableProperty
¶<<
¶>>BoundedShapeHeightEditableProperty test execution time (ms):1<<
¶>>Test Result:
¶BoundedShapeHeightEditableProperty,0.0% complete,0.0,2.0,
¶Preceding test TaggedBoundedShape failed.
¶Please correct the problems identified by preceding test:TaggedBoundedShape before running this test"), Diff(INSERT,"Running suite A2Factory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="357" _type="ConsoleOutput" date="Wed Jul 09 23:08:10 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3706701" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 23:08:10 EDT 2025<<
>>Running test TaggedFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2"), Diff(INSERT,"Wed Jul 09 23:08:10 EDT 2025<<
¶>>Running test Tagged"), Diff(EQUAL,"Factory
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="358" _type="ConsoleOutput" date="Wed Jul 09 23:08:10 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3706718" type="ConsoleOutput">
    <outputString><![CDATA[>>TaggedFactory test execution time (ms):41<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 23:08:10 EDT 2025<<
¶>>Running test TaggedFactory
¶"), Diff(INSERT,"TaggedFactory test execution time (ms):41"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="359" _type="ConsoleOutput" date="Wed Jul 09 23:08:10 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3706726" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
TaggedFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>T"), Diff(DELETE,"aggedFactory test execution time (ms):41"), Diff(INSERT,"est Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="360" _type="ConsoleOutput" date="Wed Jul 09 23:08:10 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3706731" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 23:08:10 EDT 2025<<
>>Running test BridgeSceneFactoryMethodDefined
<<
>>Running checkstyle, this will take time<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶TaggedFactory,100.0% complete,2.0,2.0,
¶"), Diff(INSERT,"Wed Jul 09 23:08:10 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined
¶<<
¶>>Running checkstyle, this will take time"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="361" _type="ConsoleOutput" date="Wed Jul 09 23:08:10 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3706776" type="ConsoleOutput">
    <outputString><![CDATA[WARNING: A terminally deprecated method in java.lang.System has been called
WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
WARNING: System::setSecurityManager will be removed in a future release
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Wed Jul 09 23:08:10 EDT 2025<<
¶>>Running test BridgeSceneFactoryMethodDefined
¶<<
¶>>Running checkstyle, this will take time<<"), Diff(INSERT,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="362" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712492" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneFactoryMethodDefined test execution time (ms):5765<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(INSERT,">>BridgeSceneFactoryMethodDefined test execution time (ms):5765<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="363" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712499" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneFactoryMethodDefined"), Diff(DELETE," test execution time (ms):5765"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="364" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712506" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 23:08:16 EDT 2025<<
>>Running test BridgeSceneSingletonFromFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 23:08:16 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"FactoryMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"SingletonFromFactory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="365" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712524" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneSingletonFromFactory test execution time (ms):18<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 23:08:16 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeSceneSingletonFromFactory"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):18"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="366" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712531" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneSingletonFromFactory"), Diff(DELETE," test execution time (ms):18"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="367" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712536" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 23:08:16 EDT 2025<<
>>Running test ConsoleSceneViewFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶BridgeSceneSingletonFromFactory,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 23:08:16 EDT 2025<<
¶>>Running test ConsoleSceneViewFactoryMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="368" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712620" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewFactoryMethodDefined test execution time (ms):88<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 23:08:16 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"ConsoleSceneViewFactoryMethodDefined"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):88"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="369" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712625" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"ConsoleSceneViewFactoryMethodDefined"), Diff(DELETE," test execution time (ms):88"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="370" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712630" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 23:08:16 EDT 2025<<
>>Running test ConsoleSceneViewSingletonFromFactory
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 23:08:16 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"ConsoleSceneView"), Diff(DELETE,"FactoryMethodDefined,100.0% complete,2.0,2.0,"), Diff(INSERT,"SingletonFromFactory"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="371" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712633" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewSingletonFromFactory test execution time (ms):3<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 23:08:16 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"ConsoleSceneViewSingletonFromFactory"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):3"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="372" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712636" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"ConsoleSceneViewSingletonFromFactory"), Diff(DELETE," test execution time (ms):3"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="373" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712640" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 23:08:16 EDT 2025<<
>>Running test LegsFactoryMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶ConsoleSceneViewSingletonFromFactory,100.0% complete,2.0,2.0,"), Diff(INSERT,"Wed Jul 09 23:08:16 EDT 2025<<
¶>>Running test LegsFactoryMethodDefined"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="374" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712743" type="ConsoleOutput">
    <outputString><![CDATA[>>LegsFactoryMethodDefined test execution time (ms):106<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 23:08:16 EDT 2025<<
¶>>Running test LegsFactoryMethodDefined
¶"), Diff(INSERT,"LegsFactoryMethodDefined test execution time (ms):106"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="375" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712749" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 23:08:16 EDT 2025<<
>>Running test A2MainCallsBridgeSceneFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"LegsFactoryMethodDefined"), Diff(DELETE," test execution time (ms):106"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 23:08:16 EDT 2025<<
¶>>Running test A2MainCallsBridgeSceneFactoryMethod
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="376" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712795" type="ConsoleOutput">
    <outputString><![CDATA[>>A2MainCallsBridgeSceneFactoryMethod test execution time (ms):45<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶LegsFactoryMethodDefined,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 23:08:16 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):45"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="377" _type="ConsoleOutput" date="Wed Jul 09 23:08:16 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712807" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 23:08:16 EDT 2025<<
>>Running test AvatarCallsLegFactoryMethod
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2MainCallsBridgeSceneFactoryMethod"), Diff(DELETE," test execution time (ms):45"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 23:08:16 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="378" _type="ConsoleOutput" date="Wed Jul 09 23:08:17 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712837" type="ConsoleOutput">
    <outputString><![CDATA[>>AvatarCallsLegFactoryMethod test execution time (ms):36<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2MainCallsBridgeSceneFactoryMethod,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 23:08:16 EDT 2025<<
¶>>Running test AvatarCallsLegFactoryMethod
¶"), Diff(INSERT,"AvatarCallsLegFactoryMethod test execution time (ms):36"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="379" _type="ConsoleOutput" date="Wed Jul 09 23:08:17 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3712843" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"AvatarCallsLegFactoryMethod"), Diff(DELETE," test execution time (ms):36"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="380" _type="ConsoleOutput" date="Wed Jul 09 23:08:34 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3730167" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2ConsoleSceneView
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶AvatarCallsLegFactoryMethod,100.0% complete,2.0,2.0,"), Diff(INSERT,"Running suite A2ConsoleSceneView"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="381" _type="ConsoleOutput" date="Wed Jul 09 23:08:34 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3730174" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 23:08:34 EDT 2025<<
>>Running test TaggedConsoleSceneView
<<
>>TaggedConsoleSceneView test execution time (ms):2<<
>>Test Result:
TaggedConsoleSceneView,100.0% complete,0.0,0.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2ConsoleSceneView"), Diff(INSERT,"Wed Jul 09 23:08:34 EDT 2025<<
¶>>Running test TaggedConsoleSceneView
¶<<
¶>>TaggedConsoleSceneView test execution time (ms):2<<
¶>>Test Result:
¶TaggedConsoleSceneView,100.0% complete,0.0,0.0,"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="382" _type="ConsoleOutput" date="Wed Jul 09 23:08:34 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3730178" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 23:08:34 EDT 2025<<
>>Running test ConsoleSceneViewGetsBridgeScene
<<
>>Wed Jul 09 23:08:34 EDT 2025<<
>>Running test TaggedLocatable
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Wed Jul 09 23:08:34 EDT 2025<<
¶>>Running test "), Diff(DELETE,"Tagged"), Diff(EQUAL,"ConsoleSceneView"), Diff(DELETE,"
¶<<
¶>>TaggedConsoleSceneView test execution time (ms):2<<
¶>>Test Result:
¶TaggedConsoleSceneView,100.0% complete,0.0,0.0,"), Diff(INSERT,"GetsBridgeScene
¶<<
¶>>Wed Jul 09 23:08:34 EDT 2025<<
¶>>Running test TaggedLocatable"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="383" _type="ConsoleOutput" date="Wed Jul 09 23:08:34 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3730183" type="ConsoleOutput">
    <outputString><![CDATA[>>Steps traced since last test:
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 23:08:34 EDT 2025<<
¶>>Running test ConsoleSceneViewGetsBridgeScene
¶<<
¶>>Wed Jul 09 23:08:34 EDT 2025<<
¶>>Running test TaggedLocatable
¶<<"), Diff(INSERT,"Steps traced since last test:"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="384" _type="ConsoleOutput" date="Wed Jul 09 23:08:34 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3730187" type="ConsoleOutput">
    <outputString><![CDATA[>>TaggedLocatable test execution time (ms):9<<
>>Test Result:
TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Steps traced since last test:"), Diff(INSERT,"TaggedLocatable test execution time (ms):9<<
¶>>Test Result:
¶TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="385" _type="ConsoleOutput" date="Wed Jul 09 23:08:34 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3730192" type="ConsoleOutput">
    <outputString><![CDATA[>>ConsoleSceneViewGetsBridgeScene test execution time (ms):12<<
>>Steps traced since last test:

>>Test Result:
ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"TaggedLocatabl"), Diff(INSERT,"ConsoleSceneViewGetsBridgeScen"), Diff(EQUAL,"e test execution time (ms):"), Diff(DELETE,"9"), Diff(INSERT,"12"), Diff(EQUAL,"<<
¶>>"), Diff(DELETE,"Test Result:
¶TaggedLocatable,0.0% complete,0.0,0.0,No class in project matching name/tag:Locatable"), Diff(INSERT,"Steps traced since last test:
¶
¶>>Test Result:
¶ConsoleSceneViewGetsBridgeScene,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="386" _type="ConsoleOutput" date="Wed Jul 09 23:08:34 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3730197" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 23:08:34 EDT 2025<<
>>Running test ConsoleSceneViewRegistersWithLocatables
<<
>>ConsoleSceneViewRegistersWithLocatables test execution time (ms):1<<
>>Test Result:
ConsoleSceneViewRegistersWithLocatables,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 23:08:34 EDT 2025<<
>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
<<
>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):1<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"ConsoleSceneViewGetsBridgeScene"), Diff(INSERT,"Wed Jul 09 23:08:34 EDT 2025<<
¶>>Running test ConsoleSceneViewRegistersWithLocatables
¶<<
¶>>ConsoleSceneViewRegistersWithLocatables"), Diff(EQUAL," test execution time (ms):1"), Diff(DELETE,"2"), Diff(EQUAL,"<<
¶"), Diff(DELETE,">>Steps traced since last test:
¶
¶"), Diff(EQUAL,">>Test Result:
¶ConsoleSceneView"), Diff(DELETE,"GetsBridgeScene"), Diff(INSERT,"RegistersWithLocatables"), Diff(EQUAL,",0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶"), Diff(INSERT,">>Wed Jul 09 23:08:34 EDT 2025<<
¶>>Running test ConsoleSceneViewPrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):1<<
¶")]]]></diff>
  </Command>
  <Command __id="387" _type="ConsoleOutput" date="Wed Jul 09 23:08:34 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3730202" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
Preceding test TaggedLocatable failed.
Please correct the problems identified by preceding test:TaggedLocatable before running this test
<<
>>Wed Jul 09 23:08:34 EDT 2025<<
>>Running test ConsoleSceneView
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 23:08:34 EDT 2025<<
¶>>Running test ConsoleSceneViewRegistersWithLocatables
¶<<
¶>>ConsoleSceneViewRegistersWithLocatables test execution time (ms):1<<
¶>>Test Result:
¶ConsoleSceneViewRegistersWithLocatables"), Diff(INSERT,"Test Result:
¶ConsoleSceneViewPrintsPropertyChangeEvent"), Diff(EQUAL,",0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 23:08:34 EDT 2025<<
¶>>Running test ConsoleSceneView"), Diff(DELETE,"PrintsPropertyChangeEvent
¶<<
¶>>ConsoleSceneViewPrintsPropertyChangeEvent test execution time (ms):1"), Diff(INSERT,"
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="388" _type="LocalChecksRawCommand" date="Wed Jul 09 23:08:34 EDT 2025" starttimestamp="1752113184173" timestamp="3730229">
    <CSVRow><![CDATA[363,Wed Jul 09 23:08:17 EDT 2025,41,0,A2Factory,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2InterfaceAsType A2NamedConstants A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,197,0,true,A2MainCallsBridgeSceneFactoryMethod AvatarCallsLegFactoryMethod BridgeSceneFactoryMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined TaggedFactory , , ,A2MainCallsBridgeSceneFactoryMethod-(2.0/2.0) AvatarCallsLegFactoryMethod-(2.0/2.0) BridgeSceneFactoryMethodDefined-(2.0/2.0) BridgeSceneSingletonFromFactory-(2.0/2.0) ConsoleSceneViewFactoryMethodDefined-(2.0/2.0) ConsoleSceneViewSingletonFromFactory-(2.0/2.0) LegsFactoryMethodDefined-(2.0/2.0) TaggedFactory-(2.0/2.0) , ,]]></CSVRow>
  </Command>
  <Command __id="389" _type="ExceptionCommand" date="Wed Jul 09 23:08:34 EDT 2025" starttimestamp="1752113184173" timestamp="3730232" type="Exception">
    <exceptionString><![CDATA[java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0
]]></exceptionString>
    <language><![CDATA[SML]]></language>
  </Command>
  <Command __id="390" _type="ConsoleOutput" date="Wed Jul 09 23:08:34 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3730233" type="ConsoleOutput">
    <outputString><![CDATA[java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Test Result:
¶ConsoleSceneViewPrintsPropertyChangeEvent,0.0% complete,0.0,0.0,
¶Preceding test TaggedLocatable failed.
¶Please correct the problems identified by preceding test:TaggedLocatable before running this test
¶<<
¶>>Wed Jul 09 23:08:34 EDT 2025<<
¶>>Running test ConsoleSceneView
¶<<"), Diff(INSERT,"java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="391" _type="ConsoleOutput" date="Wed Jul 09 23:08:34 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3730276" type="ConsoleOutput">
    <outputString><![CDATA[	at org.junit.Assert.fail(Assert.java:88)
	at org.junit.Assert.assertTrue(Assert.java:41)
	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase.java:82)
	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase.java:167)
	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase.java:204)
	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
	at java.desktop/java.awt.Component.processMouseEvent(Component.java:6629)
	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)
	at java.desktop/java.awt.Component.processEvent(Component.java:6391)
	at java.desktop/java.awt.Container.processEvent(Container.java:2266)
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"java.lang.AssertionError: No of unique event sources < 7. Old Value == New Value in less than 7 property notification. No new value before fail is an old value after fail. %0.0"), Diff(INSERT,"	at org.junit.Assert.fail(Assert.java:88)
¶	at org.junit.Assert.assertTrue(Assert.java:41)
¶	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase.java:82)
¶	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase.java:167)
¶	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase.java:204)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
¶	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
¶	at java.desktop/java.awt.Component.processMouseEvent(Component.java:6629)
¶	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)
¶	at java.desktop/java.awt.Component.processEvent(Component.java:6391)
¶	at java.desktop/java.awt.Container.processEvent(Container.java:2266)"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="392" _type="ConsoleOutput" date="Wed Jul 09 23:08:40 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3736473" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2ConsoleSceneView
<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"	at org.junit.Assert.fail(Assert.java:88)
¶	at org.junit.Assert.assertTrue(Assert.java:41)
¶	at gradingTools.comp401f16.assignment6.testcases.BridgeSceneDynamicTestCase.assertTrue(BridgeSceneDynamicTestCase.java:82)
¶	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.processPropertyChanges(ConsoleSceneViewOutputTestCase.java:167)
¶	at gradingTools.comp401f16.assignment8.testcases.ConsoleSceneViewOutputTestCase.doTest(ConsoleSceneViewOutputTestCase.java:204)
¶	at gradingTools.shared.testcases.MethodExecutionTest.test(MethodExecutionTest.java:771)
¶	at gradingTools.shared.testcases.MethodExecutionTest.defaultTest(MethodExecutionTest.java:764)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
¶	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
¶	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
¶	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
¶	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:78)
¶	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:57)
¶	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
¶	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
¶	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
¶	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
¶	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
¶	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
¶	at grader.basics.junit.AGradableJUnitTest.test(AGradableJUnitTest.java:391)
¶	at grader.basics.junit.AGradableJUnitSuite.test(AGradableJUnitSuite.java:367)
¶	at grader.basics.junit.AGradableJUnitSuite.open(AGradableJUnitSuite.java:193)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
¶	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
¶	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
¶	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
¶	at bus.uigen.reflect.local.AVirtualMethod.methodInvoke(AVirtualMethod.java:270)
¶	at bus.uigen.reflect.UnifiedMethod.invoke(UnifiedMethod.java:219)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1444)
¶	at bus.uigen.undo.BasicCommand.execute(BasicCommand.java:69)
¶	at bus.uigen.undo.AHistoryUndoer.execute(AHistoryUndoer.java:99)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1906)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1701)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethods(MethodInvocationManager.java:1585)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1538)
¶	at bus.uigen.controller.MethodInvocationManager.invokeMethod(MethodInvocationManager.java:1531)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1406)
¶	at bus.uigen.controller.MethodInvocationManager.invokeDoubleClickMethod(MethodInvocationManager.java:1357)
¶	at bus.uigen.editors.TreeAdapter.mouseClicked(TreeAdapter.java:469)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:278)
¶	at java.desktop/java.awt.AWTEventMulticaster.mouseClicked(AWTEventMulticaster.java:277)
¶	at java.desktop/java.awt.Component.processMouseEvent(Component.java:6629)
¶	at java.desktop/javax.swing.JComponent.processMouseEvent(JComponent.java:3389)
¶	at java.desktop/java.awt.Component.processEvent(Component.java:6391)
¶	at java.desktop/java.awt.Container.processEvent(Container.java:2266)"), Diff(INSERT,">>Running suite A2ConsoleSceneView
¶<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="393" _type="ConsoleOutput" date="Wed Jul 09 23:08:47 EDT 2025" overflow="false" starttimestamp="1752113184173" timestamp="3743623" type="ConsoleOutput">
    <outputString><![CDATA[Re-running test gradingTools.comp301ss21.assignment2.testcases.console_view.TaggedConsoleSceneView@7dd10804 . Results may change.
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>R"), Diff(INSERT,"Re-r"), Diff(EQUAL,"unning "), Diff(DELETE,"suite A2ConsoleSceneView
¶<<"), Diff(INSERT,"test gradingTools.comp301ss21.assignment2.testcases.console_view.TaggedConsoleSceneView@7dd10804 . Results may change."), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="394" _type="ShellCommand" date="Wed Jul 09 23:09:50 EDT 2025" starttimestamp="1752113184173" timestamp="3806004" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="395" _type="ShellCommand" date="Wed Jul 09 23:09:53 EDT 2025" starttimestamp="1752113184173" timestamp="3808956" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="396" _type="ShellCommand" date="Wed Jul 09 23:10:02 EDT 2025" starttimestamp="1752113184173" timestamp="3818795" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="397" _type="ShellCommand" date="Wed Jul 09 23:10:07 EDT 2025" starttimestamp="1752113184173" timestamp="3823108" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="398" _type="PasteCommand" date="Wed Jul 09 23:10:07 EDT 2025" starttimestamp="1752113184173" timestamp="3823501" />
  <Command __id="399" _type="ShellCommand" date="Wed Jul 09 23:10:16 EDT 2025" starttimestamp="1752113184173" timestamp="3831995" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="400" _type="CopyCommand" date="Wed Jul 09 23:10:23 EDT 2025" starttimestamp="1752113184173" timestamp="3839199" />
  <Command __id="401" _type="ShellCommand" date="Wed Jul 09 23:10:34 EDT 2025" starttimestamp="1752113184173" timestamp="3850251" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="402" _type="ShellCommand" date="Wed Jul 09 23:10:37 EDT 2025" starttimestamp="1752113184173" timestamp="3853130" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="403" _type="CopyCommand" date="Wed Jul 09 23:10:40 EDT 2025" starttimestamp="1752113184173" timestamp="3856607" />
  <Command __id="404" _type="LocalChecksRawCommand" date="Wed Jul 09 23:10:40 EDT 2025" starttimestamp="1752113184173" timestamp="3856622">
    <CSVRow><![CDATA[365,Wed Jul 09 23:08:40 EDT 2025,41,0,A2ConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2InterfaceAsType A2NamedConstants A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,197,2,true,ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables TaggedConsoleSceneView , , ,ConsoleSceneView-(0.0/50.0) ConsoleSceneViewGetsBridgeScene-(0.0/0.0) ConsoleSceneViewPrintsPropertyChangeEvent-(0.0/0.0) ConsoleSceneViewRegistersWithLocatables-(0.0/0.0) TaggedConsoleSceneView-(0.0/0.0) , ,]]></CSVRow>
  </Command>
  <Command __id="405" _type="LocalChecksRawCommand" date="Wed Jul 09 23:10:40 EDT 2025" starttimestamp="1752113184173" timestamp="3856623">
    <CSVRow><![CDATA[366,Wed Jul 09 23:08:47 EDT 2025,41,0,TaggedConsoleSceneView,A2Encapsulation A2MainCallsBridgeSceneFactoryMethod A2MnemonicNames A2NamingConventions A2NoStarImports A2NonPublicAccessModifiersMatched A2PackageDeclarations A2SimplifyBooleanExpressions A2SimplifyBooleanReturns AvatarCallsLegFactoryMethod BridgeSceneApproachMethodDefined BridgeSceneDynamics BridgeSceneFactoryMethodDefined BridgeSceneFailedMethodDefined BridgeScenePassedMethodDefined BridgeSceneSayMethodDefined BridgeSceneSingletonFromFactory ConsoleSceneViewFactoryMethodDefined ConsoleSceneViewSingletonFromFactory LegsFactoryMethodDefined Locatable_IS_A_PropertyListenerRegisterer TaggedConsoleSceneView TaggedFactory ,A2CommonPropertiesAreInherited A2CommonSignaturesAreInherited A2InterfaceAsType A2NamedConstants A2PublicMethodsOverride ,A2ExpectedSuperTypes A2NoHiddenFields BoundedShapeAnnouncesPropertyChangeEvent BoundedShapeHeightEditableProperty BoundedShapeHeightProperty BoundedShapeInstantiatesPropertyChangeEvent BoundedShapeWidthEditableProperty BoundedShapeWidthProperty BridgeSceneArthurScrollLeftArmTestCase BridgeSceneArthurScrollRightLegTestCase BridgeSceneGalahadScrollLeftArmTestCase BridgeSceneLancelotScrollLeftArmTestCase BridgeSceneRobinScrollLeftArmTestCase BridgeSceneScrollMethodDefined ConsoleSceneView ConsoleSceneViewGetsBridgeScene ConsoleSceneViewPrintsPropertyChangeEvent ConsoleSceneViewRegistersWithLocatables LocatableAnnouncesPropertyChangeEvent LocatableInstantiatesPropertyChangeEvent LocatablePropertyChangeListenersProperty LocatableXEditableProperty LocatableXProperty LocatableYEditableProperty LocatableYProperty TaggedBoundedShape TaggedLocatable , ,197,3,false, , , ,TaggedConsoleSceneView-(0.0/0.0) , ,]]></CSVRow>
  </Command>
