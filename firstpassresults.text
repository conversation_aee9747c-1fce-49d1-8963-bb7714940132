Starting audit...
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Expected type names/tags: [main.Assignment1, @Comp301Tags.ROTATING_LINE, @Comp301Tags.ANGLE, @Comp301Tags.AVATAR, @Comp301Tags.BRIDGE_SCENE, main.Assignment2, @Comp301Tags.LOCATABLE, @Comp301Tags.BOUNDED_SHAPE, @Comp301Tags.FACTORY_CLASS, @Comp301Tags.CONSOLE_SCENE_VIEW, main.Assignment3, @Comp301Tags.INHERITING_BRIDGE_SCENE_PAINTER, @Comp301Tags.PAINT_LISTENER, @Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER, @Comp301Tags.BRIDGE_SCENE_CONTROLLER, @Comp301Tags.TABLE, @Comp301Tags. PROGRESS_BAR_CREATOR] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:10:30: Named Constant SLEEP_SECONDS defined. Good! [ConstantDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:11:29: Parameter args should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:19:1: Class Data Abstraction Coupling is 8 (max allowed is 7) classes [AScaleRectangle, ArthurHead, AvatarImpl, GalahadHead, Gorge, GuardHead, LancelotHead, RobinHead]. [ClassDataAbstractionCoupling]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:23:29: Named Constant SOME_AX defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:24:29: Named Constant SOME_AY defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:25:29: Named Constant LANCELOT_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:26:29: Named Constant ROBIN_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:27:29: Named Constant GAL_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:28:29: Named Constant GUARD_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:34:30: Named Constant AREA_AX defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:35:30: Named Constant KNIGHT_AY defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:36:30: Named Constant GUARD_AY defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:37:30: Named Constant AREA_WIDTH defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:38:30: Named Constant AREA_HEIGHT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:40:30: Named Constant GORGE_AX defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:41:30: Named Constant RIGHT_SIDE_X defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:43:30: Named Constant CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:96:26: Final parameter avatar defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:105:21: Final parameter say defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:161:24: Parameter deltaX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:161:36: Parameter deltaY should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:7:35: Named Constant MAX_PRINTED_TRACES defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:8:43: Named Constant MAX_TRACES defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:9:43: Named Constant PROCESS_TIME defined. Good! [ConstantDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:10:41: Parameter args should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:31:39: Parameter leftRadius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:31:58: Parameter leftAngle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:31:76: Parameter rightRadius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:31:96: Parameter rightAngle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:21:31: Parameter defaultImageFileName should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:33:34: Parameter file should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:34:16: Variable 'oldFileName' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:45:22: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:45:26: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:46:13: Variable 'oldX' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:57:22: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:57:26: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:58:13: Variable 'oldY' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:66:43: Parameter listener should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:71:24: Parameter property should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:71:41: Parameter old should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:71:53: Parameter current should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractImageShape.java:72:29: Variable 'event' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:15:32: Parameter defaultText should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:27:25: Parameter text should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:27:32: 'text' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:28:16: Variable 'oldText' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:39:22: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:39:26: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:40:13: Variable 'oldX' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:51:22: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:51:26: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:52:13: Variable 'oldY' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:58:43: Parameter listener should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:63:24: Parameter property should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:63:41: Parameter old should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:63:53: Parameter current should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AbstractStringShape.java:64:29: Variable 'event' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:7:8: Unused import - mp.shapes.Locatable. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:8:8: Unused import - mp.shapes.Moveable. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:18:23: Final parameter h defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:44:22: Final parameter deltaX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:44:40: Final parameter deltaY defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:2:8: Unused import - mp.shapes.Get. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:19:18: Parameter leftRadius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:19:37: Parameter leftAngle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:19:55: Parameter rightRadius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:19:75: Parameter rightAngle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:36:22: Parameter deltaX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:36:34: Parameter deltaY should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:36: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:40: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:43: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:47: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:50: Parameter width should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:54: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:61: Parameter height should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:20:65: 'height' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:33:22: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:33:26: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:34:13: Variable 'oldX' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:45:22: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:45:26: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:46:13: Variable 'oldY' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:57:26: Parameter width should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:57:30: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:58:13: Variable 'oldWidth' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:60:16: The String "Width" appears 2 times in the file. [MultipleStringLiterals]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:69:27: Parameter height should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:69:31: 'height' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:70:13: Variable 'oldHeight' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:72:16: The String "Height" appears 2 times in the file. [MultipleStringLiterals]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:76:23: Parameter percentage should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:77:13: Variable 'oldWidth' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:78:13: Variable 'oldHeight' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:86:43: Parameter listener should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:91:24: Parameter property should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:91:41: Parameter old should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:91:53: Parameter current should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractAScaleRectangle.java:92:29: Variable 'event' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:13:33: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:13:40: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:13:47: Parameter width should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:13:51: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:13:58: Parameter height should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:13:62: 'height' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:25:26: Parameter width should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:25:30: 'width' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:26:13: Variable 'oldWidth' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:37:27: Parameter height should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:37:31: 'height' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractBoundedShape.java:38:13: Variable 'oldHeight' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:3:8: Unused import - util.annotations.Tags. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:12:8: Unused import - tags301.Comp301Tags. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:24:30: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:24:34: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:24:37: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:24:41: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:35:22: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:35:26: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:36:13: Variable 'oldX' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:47:22: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:47:26: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:48:13: Variable 'oldY' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:54:43: Parameter listener should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:59:24: Parameter property should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:59:41: Parameter old should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:59:53: Parameter current should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractLocatable.java:60:29: Variable 'event' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:3:8: Unused import - util.annotations.Tags. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:6:8: Unused import - tags301.Comp301Tags. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:12:31: Parameter radius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:12:38: 'radius' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:12:46: Parameter angle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:12:53: 'angle' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:19:31: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:19:38: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:30:27: Parameter radius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:30:34: 'radius' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:31:16: Variable 'oldRadius' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:34:16: The String "Radius" appears 2 times in the file. [MultipleStringLiterals]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:43:26: Parameter angle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:43:33: 'angle' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:44:16: Variable 'oldAngle' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:47:16: The String "Angle" appears 2 times in the file. [MultipleStringLiterals]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:51:13: Variable 'oldX' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:52:13: Variable 'oldY' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:60:16: Variable 'oldRadius' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:61:16: Variable 'oldAngle' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:69:22: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractPolarPoint.java:75:22: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:7:8: Unused import - util.models.PropertyListenerRegisterer. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:15:35: Named Constant UNIT defined. Good! [ConstantDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:29:22: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:29:26: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:40:22: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:40:26: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:56:26: Parameter width should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:57:16: The String "Width" appears 4 times in the file. [MultipleStringLiterals]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:61:27: Parameter height should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:62:16: The String "Height" appears 4 times in the file. [MultipleStringLiterals]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:71:27: Parameter radius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:72:16: Variable 'oldRadius' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:85:26: Parameter angle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:86:16: Variable 'oldAngle' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:94:24: Parameter units should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:99:22: Parameter deltaX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:99:34: Parameter deltaY should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:100:13: Variable 'height' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AbstractRotateLine.java:101:13: Variable 'width' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:22:23: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:22:27: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:22:30: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:22:34: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:33:22: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:33:26: 'x' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:43:22: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ALocatable.java:43:26: 'y' hides a field. [HiddenField]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:4:28: Parameter theRadius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:4:46: Parameter theAngle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:7:28: Parameter theX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:7:38: Parameter theY should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:13:32: Parameter a should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:13:39: Parameter b should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:13:46: Parameter w should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:13:53: Parameter h should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:21:36: Parameter evt should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:47:52: Parameter bridgeScene should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:47:77: Parameter consoleScene should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:64:44: Parameter avatar should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:64:59: Parameter consoleScene should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:80:52: Parameter shape should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:80:66: Parameter consoleScene should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:83:29: Variable 'compositeShape' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:14:18: Final parameter x defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:3:8: Unused import - util.annotations.Tags. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:8:8: Unused import - tags301.Comp301Tags. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:22:47: Parameter arg0 should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:27:24: Parameter property should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:27:41: Parameter old should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:27:53: Parameter current should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:28:29: Variable 'event' should be declared final. [FinalLocalVariable]
Audit done.
