	<module name="ClassDefined">
		<property name="severity" value="info"/>
		<property name="expectedTypes" value="
			@Comp301Tags.ANGLE,
			@Comp301Tags.AVATAR,
			@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.BRIDGE_SCENE,
			@Comp301Tags.CONSOLE_SCENE_VIEW,
			@Comp301Tags.FACTORY_CLASS,
			@Comp301Tags.LOCATABLE,
			@Comp301Tags.LOCATABLE+@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.ROTATING_LINE,
			@main.Assignment2,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedSignatures" value="
			main:String[]->.*,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedSignatures" value="
			main:String[]->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedCalls" value="
			@Comp301Tags.BRIDGE_SCENE!passed:->void,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.FACTORY_CLASS!legsFactoryMethod:->@Comp301Tags.ANGLE,
			@Comp301Tags.CONSOLE_SCENE_VIEW!registerWithBridgeScene:@Comp301Tags.BRIDGE_SCENE;@Comp301Tags.CONSOLE_SCENE_VIEW->void,
			@Comp301Tags.BRIDGE_SCENE!getKnightArea:->@Comp301Tags.BOUNDED_SHAPE,
			bus.uigen.ObjectEditor!edit:Object->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.AVATAR!getStringShape:->.*,
			@Comp301Tags.LOCATABLE!setAngle:double->void,
			@Comp301Tags.BRIDGE_SCENE!getRobin:->@Comp301Tags.AVATAR,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.AVATAR!getArms:->@Comp301Tags.ANGLE,
			@Comp301Tags.FACTORY_CLASS!bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.AVATAR!getLegs:->@Comp301Tags.ANGLE,
			@Comp301Tags.BRIDGE_SCENE!getGuardArea:->@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.LOCATABLE!getWidth:->int,
			java.lang.Math!atan2:double;double->double,
			@Comp301Tags.BRIDGE_SCENE!approach:@Comp301Tags.AVATAR->void,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.BRIDGE_SCENE!failed:->void,
			@Comp301Tags.LOCATABLE!setRadius:double->void,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.BRIDGE_SCENE!getGuard:->@Comp301Tags.AVATAR,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.BRIDGE_SCENE!getGalahad:->@Comp301Tags.AVATAR,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.CONSOLE_SCENE_VIEW!consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			@Comp301Tags.BRIDGE_SCENE!say:String->void,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.BRIDGE_SCENE!getArthur:->@Comp301Tags.AVATAR,
			@Comp301Tags.BRIDGE_SCENE!getLancelot:->@Comp301Tags.AVATAR,
			util.models.PropertyListenerRegisterer!addPropertyChangeListener:java.beans.PropertyChangeListener->void,
			@Comp301Tags.ANGLE!getLeftLine:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.FACTORY_CLASS!consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			@Comp301Tags.ANGLE!getRightLine:*->.*,
			java.lang.Thread!sleep:long->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedCalls" value="
			@Comp301Tags.BRIDGE_SCENE!passed:->void,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.FACTORY_CLASS!legsFactoryMethod:->@Comp301Tags.ANGLE,
			@Comp301Tags.CONSOLE_SCENE_VIEW!registerWithBridgeScene:@Comp301Tags.BRIDGE_SCENE;@Comp301Tags.CONSOLE_SCENE_VIEW->void,
			@Comp301Tags.BRIDGE_SCENE!getKnightArea:->@Comp301Tags.BOUNDED_SHAPE,
			bus.uigen.ObjectEditor!edit:Object->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.AVATAR!getStringShape:->.*,
			@Comp301Tags.LOCATABLE!setAngle:double->void,
			@Comp301Tags.BRIDGE_SCENE!getRobin:->@Comp301Tags.AVATAR,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.AVATAR!getArms:->@Comp301Tags.ANGLE,
			@Comp301Tags.FACTORY_CLASS!bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.AVATAR!getLegs:->@Comp301Tags.ANGLE,
			@Comp301Tags.BRIDGE_SCENE!getGuardArea:->@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.LOCATABLE!getWidth:->int,
			java.lang.Math!atan2:double;double->double,
			@Comp301Tags.BRIDGE_SCENE!approach:@Comp301Tags.AVATAR->void,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.BRIDGE_SCENE!failed:->void,
			@Comp301Tags.LOCATABLE!setRadius:double->void,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.BRIDGE_SCENE!getGuard:->@Comp301Tags.AVATAR,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.BRIDGE_SCENE!getGalahad:->@Comp301Tags.AVATAR,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.CONSOLE_SCENE_VIEW!consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			@Comp301Tags.BRIDGE_SCENE!say:String->void,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.BRIDGE_SCENE!getArthur:->@Comp301Tags.AVATAR,
			@Comp301Tags.BRIDGE_SCENE!getLancelot:->@Comp301Tags.AVATAR,
			util.models.PropertyListenerRegisterer!addPropertyChangeListener:java.beans.PropertyChangeListener->void,
			@Comp301Tags.ANGLE!getLeftLine:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.FACTORY_CLASS!consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			@Comp301Tags.ANGLE!getRightLine:*->.*,
			java.lang.Thread!sleep:long->void,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value="
			KnightTurn:boolean,
			InteractingKnight:@Comp301Tags.AVATAR,
			Gorge:.*,
			Occupied:boolean,
			Arthur:@Comp301Tags.AVATAR,
			KnightArea:@Comp301Tags.BOUNDED_SHAPE,
			Guard:@Comp301Tags.AVATAR,
			Lancelot:@Comp301Tags.AVATAR,
			GuardArea:@Comp301Tags.BOUNDED_SHAPE,
			Galahad:@Comp301Tags.AVATAR,
			Robin:@Comp301Tags.AVATAR,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value="
			KnightTurn:boolean,
			InteractingKnight:@Comp301Tags.AVATAR,
			Gorge:.*,
			Occupied:boolean,
			Arthur:@Comp301Tags.AVATAR,
			KnightArea:@Comp301Tags.BOUNDED_SHAPE,
			Guard:@Comp301Tags.AVATAR,
			Lancelot:@Comp301Tags.AVATAR,
			GuardArea:@Comp301Tags.BOUNDED_SHAPE,
			Galahad:@Comp301Tags.AVATAR,
			Robin:@Comp301Tags.AVATAR,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedSignatures" value="
			failed:->void,
			passed:->void,
			scroll:int;int->void,
			approach:@Comp301Tags.AVATAR->void,
			say:String->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedSignatures" value="
			failed:->void,
			passed:->void,
			scroll:int;int->void,
			approach:@Comp301Tags.AVATAR->void,
			say:String->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			java.lang.Math!atan2:double;double->double,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.AVATAR!getStringShape:->.*,
			@Comp301Tags.ANGLE!getLeftLine:*->.*,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.ANGLE!getRightLine:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			java.lang.Math!atan2:double;double->double,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.AVATAR!getStringShape:->.*,
			@Comp301Tags.ANGLE!getLeftLine:*->.*,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.ANGLE!getRightLine:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedSignatures" value="
			consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			legsFactoryMethod:->@Comp301Tags.ANGLE,
			bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
			legsFactoryMethod:double;double;double;double->@Comp301Tags.ANGLE,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedSignatures" value="
			consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			legsFactoryMethod:->@Comp301Tags.ANGLE,
			bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
			legsFactoryMethod:double;double;double;double->@Comp301Tags.ANGLE,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedCalls" value="
			java.lang.Math!atan2:double;double->double,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.LOCATABLE!setRadius:double->void,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.CONSOLE_SCENE_VIEW!consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.LOCATABLE!setAngle:double->void,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedCalls" value="
			java.lang.Math!atan2:double;double->double,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.LOCATABLE!setRadius:double->void,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.CONSOLE_SCENE_VIEW!consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.LOCATABLE!setAngle:double->void,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			Legs:@Comp301Tags.ANGLE,
			Head:@Comp301Tags.BOUNDED_SHAPE,
			StringShape:.*,
			Arms:@Comp301Tags.ANGLE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			Legs:@Comp301Tags.ANGLE,
			Head:@Comp301Tags.BOUNDED_SHAPE,
			StringShape:.*,
			Arms:@Comp301Tags.ANGLE,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			java.lang.Math!atan2:double;double->double,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.AVATAR!move:*->.*,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			java.lang.Math!atan2:double;double->double,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.ANGLE!move:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.AVATAR!move:*->.*,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value="
			RightLine:@Comp301Tags.LOCATABLE,
			LeftLine:@Comp301Tags.LOCATABLE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value="
			RightLine:@Comp301Tags.LOCATABLE,
			LeftLine:@Comp301Tags.LOCATABLE,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			java.lang.Math!atan2:double;double->double,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			java.lang.Math!atan2:double;double->double,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.lang.Math!sqrt:double->double,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
			scale:int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
			scale:int->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!notify:String;Object;Object->void,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
			scale:int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
			scale:int->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE+@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE+@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE+@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE+@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE+@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE+@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE+@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE+@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE+@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE+@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			move:int;int->void,
			rotate:int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			move:int;int->void,
			rotate:int->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedInterfaces" value="
			java.beans.PropertyChangeListener,
			java.util.EventListener,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedInterfaces" value="
			java.beans.PropertyChangeListener,
			java.util.EventListener,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedProperties" value="
			PropertyChangeEvents:java.util.List,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedProperties" value="
			PropertyChangeEvents:java.util.List,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedSignatures" value="
			propertyChange:java.beans.PropertyChangeEvent->void,
			registerWithBridgeScene:@Comp301Tags.BRIDGE_SCENE;@Comp301Tags.CONSOLE_SCENE_VIEW->void,
			consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			clearPropertyChangeEvents:->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedSignatures" value="
			propertyChange:java.beans.PropertyChangeEvent->void,
			registerWithBridgeScene:@Comp301Tags.BRIDGE_SCENE;@Comp301Tags.CONSOLE_SCENE_VIEW->void,
			consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			clearPropertyChangeEvents:->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedCalls" value="
			@Comp301Tags.BRIDGE_SCENE!getKnightArea:->@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.BRIDGE_SCENE!getGuard:->@Comp301Tags.AVATAR,
			@Comp301Tags.BRIDGE_SCENE!getGalahad:->@Comp301Tags.AVATAR,
			java.util.List!clear:->void,
			java.util.List!add:Object->boolean,
			java.util.EventObject!getSource:->Object,
			java.beans.PropertyChangeEvent!getPropertyName:->String,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			java.io.PrintStream!println:*->.*,
			java.lang.Object!getClass:->Class,
			java.beans.PropertyChangeEvent!getNewValue:->Object,
			java.beans.PropertyChangeEvent!getSimpleName:*->.*,
			@Comp301Tags.AVATAR!getStringShape:->.*,
			@Comp301Tags.BRIDGE_SCENE!getArthur:->@Comp301Tags.AVATAR,
			util.models.PropertyListenerRegisterer!addPropertyChangeListener:java.beans.PropertyChangeListener->void,
			@Comp301Tags.BRIDGE_SCENE!getLancelot:->@Comp301Tags.AVATAR,
			@Comp301Tags.BRIDGE_SCENE!getRobin:->@Comp301Tags.AVATAR,
			@Comp301Tags.ANGLE!getLeftLine:*->.*,
			java.beans.PropertyChangeEvent!getOldValue:->Object,
			@Comp301Tags.AVATAR!getArms:->@Comp301Tags.ANGLE,
			@Comp301Tags.ANGLE!getRightLine:*->.*,
			@Comp301Tags.AVATAR!getLegs:->@Comp301Tags.ANGLE,
			@Comp301Tags.BRIDGE_SCENE!getGuardArea:->@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="expectedCalls" value="
			@Comp301Tags.BRIDGE_SCENE!getKnightArea:->@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.BRIDGE_SCENE!getGuard:->@Comp301Tags.AVATAR,
			@Comp301Tags.BRIDGE_SCENE!getGalahad:->@Comp301Tags.AVATAR,
			java.util.List!clear:->void,
			java.util.List!add:Object->boolean,
			java.util.EventObject!getSource:->Object,
			java.beans.PropertyChangeEvent!getPropertyName:->String,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			java.io.PrintStream!println:*->.*,
			java.lang.Object!getClass:->Class,
			java.beans.PropertyChangeEvent!getNewValue:->Object,
			java.beans.PropertyChangeEvent!getSimpleName:*->.*,
			@Comp301Tags.AVATAR!getStringShape:->.*,
			@Comp301Tags.BRIDGE_SCENE!getArthur:->@Comp301Tags.AVATAR,
			util.models.PropertyListenerRegisterer!addPropertyChangeListener:java.beans.PropertyChangeListener->void,
			@Comp301Tags.BRIDGE_SCENE!getLancelot:->@Comp301Tags.AVATAR,
			@Comp301Tags.BRIDGE_SCENE!getRobin:->@Comp301Tags.AVATAR,
			@Comp301Tags.ANGLE!getLeftLine:*->.*,
			java.beans.PropertyChangeEvent!getOldValue:->Object,
			@Comp301Tags.AVATAR!getArms:->@Comp301Tags.ANGLE,
			@Comp301Tags.ANGLE!getRightLine:*->.*,
			@Comp301Tags.AVATAR!getLegs:->@Comp301Tags.ANGLE,
			@Comp301Tags.BRIDGE_SCENE!getGuardArea:->@Comp301Tags.BOUNDED_SHAPE,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.CONSOLE_SCENE_VIEW"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.LOCATABLE,
		"/>
	</module>
	<module name="ExpectedSuperTypes">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedSuperTypes" value="
			@Comp301Tags.LOCATABLE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			move:int;int->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
			rotate:int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			move:int;int->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
			rotate:int->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			@Comp301Tags.LOCATABLE!setAngle:double->void,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			@Comp301Tags.LOCATABLE!setAngle:double->void,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ROTATING_LINE"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
