package mp.shapes;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Visible;
import util.models.PropertyListenerRegisterer;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.List;
import java.util.ArrayList;
import tags301.Comp301Tags;

@StructurePattern(StructurePatternNames.LINE_PATTERN)
public abstract class AbstractLocatable implements Locatable, PropertyListenerRegisterer {
    protected int x, y;
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();
    
    public AbstractLocatable() {
        this.x = 0;
        this.y = 0;
    }
    
    public AbstractLocatable(int x, int y) {
        this.x = x;
        this.y = y;
    }
    
    @Override
    public int getX() {
        return x;
    }
    
    @Override
    public void setX(int x) {
        int oldX = this.x;
        this.x = x;
        notify("X", oldX, x);
    }
    
    @Override
    public int getY() {
        return y;
    }
    
    @Override
    public void setY(int y) {
        int oldY = this.y;
        this.y = y;
        notify("Y", oldY, y);
    }
    
    @Override
    public void addPropertyChangeListener(PropertyChangeListener listener) {
        propertyChangeListeners.add(listener);
    }
    
    @Visible(false)
    public void notify(String property, Object old, Object current) {
        PropertyChangeEvent event = new PropertyChangeEvent(this, property, old, current);
        for (PropertyChangeListener listener : propertyChangeListeners) {
            listener.propertyChange(event);
        }
    }
}
